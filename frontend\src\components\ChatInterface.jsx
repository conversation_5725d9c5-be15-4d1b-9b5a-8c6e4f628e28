import React, { useState, useRef, useEffect, useCallback } from 'react';
import axios from 'axios';
import {
  Send,
  Mic,
  Plus,
  MessageSquare,
  Trash2,
  Settings,
  Volume2,
  VolumeX,
  Loader,
  ChevronDown,
  X
} from 'lucide-react';
import Modal from './Modal';

// Format seconds to HH:MM:SS format
const formatTimeToHMS = (seconds) => {
  if (!seconds && seconds !== 0) return "00:00:00";

  // Convert to number if it's a string
  const totalSeconds = Number(seconds);

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const secs = Math.floor(totalSeconds % 60);

  // Format with leading zeros
  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = secs.toString().padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};

// Global audio manager
if (typeof window.audioManager === 'undefined') {
  window.audioManager = {
    currentAudio: null,
    currentMessageId: null,
    isPlaying: false
  };
}

// Sidebar Chat Item Component
const ChatListItem = ({ chat, isActive, onSelect, onDelete }) => {
  const [isHovering, setIsHovering] = useState(false);

  return (
    <div
      className={`
        group relative flex items-center justify-between p-3 cursor-pointer
        ${isActive ? 'bg-gray-100' : 'hover:bg-gray-50'}
        transition-colors duration-200
      `}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onClick={onSelect}
    >
      <div className="flex items-center space-x-3">
        <MessageSquare className="w-5 h-5 text-gray-500" />
        <span className="text-sm truncate max-w-[150px]">
          {chat.title || 'New Chat'}
        </span>
      </div>
      {isHovering && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(chat.id);
          }}
          className="text-red-500 hover:bg-red-50 p-1 rounded-full"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      )}
    </div>
  );
};

// Message Component with Audio
const MessageWithAudio = ({ message, onToggleAudio, onTimestampClick }) => {
  return (
    <div
      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      <div
        className={`
          max-w-2xl w-full p-4 rounded-2xl
          ${message.sender === 'user'
            ? 'bg-indigo-500 text-white'
            : 'bg-white border border-gray-200 text-gray-800'
          }
        `}
      >
        <div className="text-base leading-relaxed">{message.text}</div>
        <div className="flex justify-between items-center">
          <div className={`
            text-xs mt-2 opacity-60
            ${message.sender === 'user' ? 'text-indigo-100 text-right' : 'text-gray-500 text-left'}
          `}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
          {message.sender === 'bot' && message.audioUrl && (
            <button
              onClick={() => onToggleAudio(message.id)}
              className="text-gray-500 hover:text-indigo-600"
            >
              {message.isPlaying ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </button>
          )}
        </div>
        {/* {message.context && message.context.length > 0 && (
          <div className="mt-2 text-xs italic text-gray-400">
            Contexts: {message.context.join(', ')}
          </div>
        )} */}
        {message.contextMetadata && message.contextMetadata.length > 0 && (
          <div className="mt-2 overflow-x-auto">
            <div className="flex flex-col space-y-2">
              {message.contextMetadata.map((item, index) => (
                item.metadata && item.metadata.start_time && (
                  <button
                    key={index}
                    onClick={() => onTimestampClick(item.metadata.video_id, item.metadata.start_time, message.id, message.context.join('\n\n'))}
                    className="px-2 py-1 bg-gray-100 hover:bg-indigo-100 rounded text-xs text-gray-600 hover:text-indigo-700 transition-colors cursor-pointer text-left"
                  >
                    <span className="font-semibold">{item.metadata.video_id || "Unknown file"}: </span>
                    <span className="whitespace-nowrap">
                      {formatTimeToHMS(item.metadata.start_time)} - {formatTimeToHMS(item.metadata.end_time)}
                    </span>
                  </button>
                )
              ))}
            </div>
          </div>
        )}
        {message.audioUrl && message.isPlaying && (
          <audio
            src={message.audioUrl}
            autoPlay
            onEnded={() => onToggleAudio(message.id)}
          />
        )}
      </div>
    </div>
  );
};

// Main Chat Interface
const ChatInterface = () => {
  const [chats, setChats] = useState([
    {
      id: 1,
      title: 'GENERAL',
      messages: [
        {
          id: 1,
          text: "Hi there! I'm your AI assistant. How can I help you today? I'm in GENERAL mode, so I won't use any specific context for our conversation.",
          sender: 'bot',
          timestamp: new Date(),
          context: [],
          audioUrl: null,
          isPlaying: false,
          contextMetadata: []
        }
      ],
      contextId: 'GENERAL' // Special context ID for general chat without context retrieval
    }
  ]);
  const [activeChatId, setActiveChatId] = useState(1);
  const [inputMessage, setInputMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [selectedModel, setSelectedModel] = useState('llama-3.1-8b-instant');
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pendingNewChatId, setPendingNewChatId] = useState(null);
  const [currentVideo, setCurrentVideo] = useState(null);
  const [currentMessage, setCurrentMessage] = useState(null);
  const [currentMessageContext, setCurrentMessageContext] = useState(null);
  const [loadedVideos, setLoadedVideos] = useState(new Set());
  const [videoPanelHeight, setVideoPanelHeight] = useState(250); // Default height for video panel
  const [isDragging, setIsDragging] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState(null);
  const [isLoadingTranscript, setIsLoadingTranscript] = useState(false);
  const messagesEndRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const videoPlayerRef = useRef(null);

  // List of available models
  const availableModels = [
    'llama-3.3-70b-versatile',
    'llama-3.1-8b-instant',
    'gemma2-9b-it',
    'meta-llama/llama-4-maverick-17b-128e-instruct',
    'deepseek-r1-distill-llama-70b',
    'qwen-qwq-32b',
    'compound-beta / mini'
  ];

  // Get active chat
  const activeChat = chats.find(chat => chat.id === activeChatId);

  // Scroll to bottom when messages update
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [activeChat.messages]);

  // Handle mouse events for resizing video panel
  const handleMouseDown = (e) => {
    setIsDragging(true);
    e.preventDefault();
  };

  const handleMouseMove = useCallback((e) => {
    if (!isDragging) return;

    // Calculate new height based on mouse position
    const chatContainer = document.querySelector('.flex-1');
    if (chatContainer) {
      const rect = chatContainer.getBoundingClientRect();
      const newHeight = e.clientY - rect.top - 60; // Subtract header height

      // Set minimum and maximum heights
      const minHeight = 150;
      const maxHeight = rect.height * 0.6; // Max 60% of available height

      if (newHeight >= minHeight && newHeight <= maxHeight) {
        setVideoPanelHeight(newHeight);
      }
    }
  }, [isDragging, setVideoPanelHeight]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, [setIsDragging]);

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'ns-resize';
      document.body.style.userSelect = 'none';
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Handle video timestamp click
  const handleTimestampClick = async (videoId, startTime, message_id, message_context) => {
    try {
      // Check if video is already loaded
      if (!loadedVideos.has(videoId) || currentVideo !== videoId) {
        // Direct access to static video file
        const videoUrl = `http://localhost:8000/videos/${videoId + ".mp4"}`;
        setCurrentVideo(videoId);
        setLoadedVideos(prev => new Set([...prev, videoId]));
        setCurrentMessage(message_id);
        setCurrentMessageContext(message_context);

        // Wait for the video element to be available and then set source
        await new Promise((resolve) => {
          const checkVideoRef = () => {
            if (videoPlayerRef.current) {
              videoPlayerRef.current.src = videoUrl;
              videoPlayerRef.current.onloadeddata = resolve;
            } else {
              // If ref is not available yet, wait a bit and try again
              setTimeout(checkVideoRef, 10);
            }
          };
          checkVideoRef();
        });
      }

      // Seek to timestamp
      if (videoPlayerRef.current) {
        videoPlayerRef.current.currentTime = startTime;
      }
    } catch (error) {
      console.error('Error loading video:', error);
    }
  };

  // Fetch transcript for current video
  const fetchTranscriptForVideo = async (videoId) => {
    if (!videoId) {
      setCurrentTranscript(null);
      return;
    }

    try {
      setIsLoadingTranscript(true);

      // First get all transcripts to find the matching one
      const transcriptsResponse = await axios.get('http://localhost:8000/transcripts/');
      const transcripts = transcriptsResponse.data;

      // Find transcript that matches the video filename
      const matchingTranscript = transcripts.find(t =>
        t.filename === `${videoId}.mp4` ||
        t.id.includes(videoId) ||
        t.filename.includes(videoId)
      );

      if (matchingTranscript) {
        // Fetch the full transcript content
        const transcriptResponse = await axios.get(`http://localhost:8000/transcripts/${matchingTranscript.id}`);
        setCurrentTranscript(transcriptResponse.data.transcription);
      } else {
        setCurrentTranscript(null);
      }
    } catch (error) {
      console.error('Error fetching transcript:', error);
      setCurrentTranscript(null);
    } finally {
      setIsLoadingTranscript(false);
    }
  };

  // Effect to fetch transcript when current video changes
  useEffect(() => {
    fetchTranscriptForVideo(currentVideo);
  }, [currentVideo]);

  // Create a new chat
  const createNewChat = () => {
    // Generate a temporary chat ID
    const newChatId = Date.now();
    // Set the pending chat ID to be created after getting the identifier
    setPendingNewChatId(newChatId);
    // Open the modal to request the context identifier
    setIsModalOpen(true);
  };

  // Handle the modal submission with chat name and selected videos
  const handleModalSubmit = async (data) => {
    if (!pendingNewChatId) return;

    const { chatName, selectedVideos } = data;

    try {
      // Call the backend to create a merged index for the selected videos
      const response = await axios.post('http://localhost:8000/merge_embeddings', {
        chat_id: chatName,
        video_ids: selectedVideos
      });

      // Create a new chat with the provided chat name and merged context
      setChats(prev => [
        ...prev,
        {
          id: pendingNewChatId,
          title: chatName,
          messages: [
            {
              id: 1,
              text: "Hi there! I'm your AI assistant. How can I help you today?",
              sender: 'bot',
              timestamp: new Date(),
              context: [],
              audioUrl: null,
              isPlaying: false,
              contextMetadata: []
            }
          ],
          contextId: chatName, // Use the chat name as the context ID
          videoIds: selectedVideos // Store the selected video IDs with the chat
        }
      ]);

      // Set the active chat to the new chat
      setActiveChatId(pendingNewChatId);

    } catch (error) {
      console.error('Error creating merged index:', error);
      // Create the chat anyway, but with a warning message
      setChats(prev => [
        ...prev,
        {
          id: pendingNewChatId,
          title: chatName,
          messages: [
            {
              id: 1,
              text: "Hi there! I'm your AI assistant. There was an issue preparing your selected videos. Some context might not be available.",
              sender: 'bot',
              timestamp: new Date(),
              context: [],
              audioUrl: null,
              isPlaying: false,
              contextMetadata: []
            }
          ],
          contextId: chatName,
          videoIds: selectedVideos
        }
      ]);
      setActiveChatId(pendingNewChatId);
    }

    // Reset the pending chat ID
    setPendingNewChatId(null);
  };

  // Delete a chat
  const deleteChat = (chatId) => {
    setChats(prev => {
      const remainingChats = prev.filter(chat => chat.id !== chatId);

      // If deleting the active chat, switch to another or create new
      if (chatId === activeChatId) {
        if (remainingChats.length > 0) {
          setActiveChatId(remainingChats[0].id);
        } else {
          createNewChat();
        }
      }

      return remainingChats;
    });
  };

  // Toggle audio playback
  const toggleAudio = async (messageId) => {
    const manager = window.audioManager;

    // First try to find the message in the active chat
    const activeChat = chats.find(chat => chat.id === activeChatId);

    // Look for the message in the active chat first
    let message = activeChat?.messages.find(msg => msg.id === messageId);

    // If not found in active chat, search all chats
    if (!message) {
      for (const chat of chats) {
        const foundMessage = chat.messages.find(msg => msg.id === messageId);
        if (foundMessage) {
          message = foundMessage;
          break;
        }
      }
    }

    console.log('Message ID:', messageId);
    console.log('Active chat ID:', activeChatId);
    console.log('Found message:', message)

    if (!message || !message.audioUrl) {
      console.log(message)
      console.error("No message found or no audio URL available for message:", messageId);
      return;
    }

    console.log(message.audioUrl)

    // const audioUrl = message.audioUrl;
    const backendUrl = "http://localhost:8000";  // Update this if FastAPI runs on a different port
    const audioUrl = message.audioUrl.startsWith("./")
      ? `${backendUrl}${message.audioUrl.slice(1)}` // Remove leading "./" and prepend backend URL
      : message.audioUrl;

    console.log("Updated AUDIO URL:", audioUrl);

    // Case 1: No audio is playing
    if (!manager.currentAudio) {
      const audio = new Audio(audioUrl);

      console.log("AUDIO URL: ", audioUrl)

      audio.addEventListener('ended', () => {
        // Clean up when audio finishes
        window.audioManager.currentAudio = null;
        window.audioManager.currentMessageId = null;
        window.audioManager.isPlaying = false;
      });

      try {
        await audio.play();
        // Store current audio info
        manager.currentAudio = audio;
        manager.currentMessageId = messageId;
        manager.isPlaying = true;
      } catch (err) {
        console.error("Error playing audio:", err);
      }

      return;
    }

    // Case 2: This message's audio is already playing - pause it
    if (manager.currentMessageId === messageId) {
      manager.currentAudio.pause();
      manager.currentAudio = null;
      manager.currentMessageId = null;
      manager.isPlaying = false;
      return;
    }

    // Case 3: Another message's audio is playing - stop it and play this one
    manager.currentAudio.pause();
    manager.currentAudio = null;

    const audio = new Audio(audioUrl);

    audio.addEventListener('ended', () => {
      // Clean up when audio finishes
      window.audioManager.currentAudio = null;
      window.audioManager.currentMessageId = null;
      window.audioManager.isPlaying = false;
    });

    try {
      await audio.play();
      // Store current audio info
      manager.currentAudio = audio;
      manager.currentMessageId = messageId;
      manager.isPlaying = true;
    } catch (err) {
      console.error("Error playing audio:", err);
    }
  };

  // Fetch audio transcription
  const fetchAudioTranscription = async (message) => {
    try {
      const response = await axios.post('http://localhost:8000/tts', {
        text: message.text
      });

      console.log("THE AUDIO", response)

      // Update the message with audio URL if available
      setChats(prev => prev.map(chat => {
        if (chat.id === activeChatId) {
          const updatedMessages = chat.messages.map(msg =>
            msg.id === message.id
              ? { ...msg, audioUrl: response.data.audio_url }
              : msg
          );
          return { ...chat, messages: updatedMessages };
        }
        return chat;
      }));
    } catch (error) {
      console.error('Error fetching audio transcription:', error);
      // Inform user about audio transcription failure
      setChats(prev => prev.map(chat => {
        if (chat.id === activeChatId) {
          const updatedMessages = chat.messages.map(msg =>
            msg.id === message.id
              ? {
                ...msg,
                audioUrl: null,
                transcriptionError: "Audio transcription not available"
              }
              : msg
          );
          return { ...chat, messages: updatedMessages };
        }
        return chat;
      }));
    }
  };

  // Send message handler
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isProcessing) return;

    setIsProcessing(true);
    const text = inputMessage;
    setInputMessage('');

    // Find active chat
    const chatIndex = chats.findIndex(chat => chat.id === activeChatId);
    if (chatIndex === -1) return;

    // Create a new message object
    const newUserMessage = {
      id: Date.now(),
      text: text,
      sender: 'user',
      timestamp: new Date(),
      context: [],
      audioUrl: null,
      isPlaying: false
    };

    // Update chat with user message
    const updatedChats = [...chats];
    updatedChats[chatIndex].messages.push(newUserMessage);
    setChats(updatedChats);

    try {
      // Update chat title if it's a new chat with only the welcome message
      if (updatedChats[chatIndex].messages.length === 2 && !updatedChats[chatIndex].title) {
        const title = text.slice(0, 30) + (text.length > 30 ? '...' : '');
        updatedChats[chatIndex].title = title;
        setChats(updatedChats);
      }

      // Get the current chat's context ID
      const currentChat = chats.find(chat => chat.id === activeChatId);
      const contextId = currentChat?.contextId || 'default';

      // Call API to get bot response with context ID
      const response = await axios.post('http://localhost:8000/chat_primary', {
        query: text,
        top_k: 3,  // Default to retrieving top 3 most similar texts
        model: selectedModel,  // Include the selected model in the request
        context_id: contextId  // Pass the context ID from the chat
      });

      // Create bot message with response
      const botMessage = {
        id: Date.now() + 1,
        text: response.data.response,
        sender: 'bot',
        timestamp: new Date(),
        context: response.data.retrieved_contexts || [],
        audioUrl: response.data.tts_path || null,
        isPlaying: false,
        contextMetadata: response.data.context_metadata || []
      };

      // Update chat with bot message
      updatedChats[chatIndex].messages.push(botMessage);
      setChats([...updatedChats]);
    } catch (error) {
      console.error('Error getting bot response:', error);

      // Create error message
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I encountered an error processing your request. Please try again later.",
        sender: 'bot',
        timestamp: new Date(),
        context: [],
        audioUrl: null,
        isPlaying: false,
        contextMetadata: []
      };

      // Update chat with error message
      updatedChats[chatIndex].messages.push(errorMessage);
      setChats([...updatedChats]);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceInput = () => {
    if (!isListening) {
      // Start recording
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          setIsListening(true);
          console.log('Started listening...');

          // Create a new MediaRecorder instance
          const mediaRecorder = new MediaRecorder(stream);
          mediaRecorderRef.current = mediaRecorder;
          audioChunksRef.current = [];

          // Event handler for when data is available
          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              audioChunksRef.current.push(event.data);
            }
          };

          // Event handler for when recording stops
          mediaRecorder.onstop = () => {
            // Combine audio chunks into a single blob
            const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

            // Create a FormData object to send the audio file
            const formData = new FormData();
            formData.append('file', audioBlob, 'recording.webm');

            // Set transcribing state to true
            setIsTranscribing(true);

            // Send the audio for transcription
            axios.post('/transcribe/', formData, {
              headers: {
                'Content-Type': 'multipart/form-data'
              }
            })
              .then(response => {
                // Set the transcription as the input message
                const transcription = response.data.transcription;
                setInputMessage(transcription);
                setIsTranscribing(false);

                // Auto-send the transcribed message if it's not empty
                if (transcription && transcription.trim().length > 0) {
                  // Small timeout to allow user to see what was transcribed
                  setTimeout(() => handleSendMessage(), 500);
                }

                // Clean up the stream tracks
                stream.getTracks().forEach(track => track.stop());
              })
              .catch(error => {
                console.error('Error transcribing audio:', error);
                setIsTranscribing(false);

                // Show error message in input field
                setInputMessage('Error transcribing audio. Please try again.');

                // Clean up the stream tracks
                stream.getTracks().forEach(track => track.stop());
              });
          };

          // Start recording
          mediaRecorder.start();
        })
        .catch(error => {
          console.error('Error accessing microphone:', error);
          setIsListening(false);

          // Show error message in input field
          setInputMessage('Error accessing microphone. Please check permissions.');
        });
    } else {
      // Stop recording if currently listening
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
        console.log('Stopped listening...');
      }
      setIsListening(false);
    }
  };

  return (
    <div className="flex h-screen bg-white">
      {/* Sidebar */}
      <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
        {/* New Chat Button */}
        <button
          onClick={createNewChat}
          className="m-4 flex items-center justify-center space-x-2 bg-indigo-500 text-white p-3 rounded-lg hover:bg-indigo-600 transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span>New Chat</span>
        </button>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {chats.map(chat => (
            <ChatListItem
              key={chat.id}
              chat={chat}
              isActive={chat.id === activeChatId}
              onSelect={() => setActiveChatId(chat.id)}
              onDelete={deleteChat}
            />
          ))}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-800">
            {activeChat.title || 'New Chat'}
          </h2>
          <div className="flex items-center space-x-2">
            {/* Model Selection Dropdown */}
            <div className="relative mr-3">
              <button
                onClick={() => setIsModelDropdownOpen(!isModelDropdownOpen)}
                className="flex items-center text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-2 rounded-md transition-colors"
              >
                <span className="mr-1">Model: {selectedModel}</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {isModelDropdownOpen && (
                <div className="absolute right-0 mt-1 w-80 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200">
                  {availableModels.map(model => (
                    <button
                      key={model}
                      onClick={() => {
                        setSelectedModel(model);
                        setIsModelDropdownOpen(false);
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm ${selectedModel === model ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50'}`}
                    >
                      {model}
                    </button>
                  ))}
                </div>
              )}
            </div>

            <button className="text-gray-500 hover:bg-gray-100 p-2 rounded-full">
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Video Player */}
        {currentVideo && (
          <>
            <div
              className="bg-gradient-to-br from-slate-50 to-gray-100 border-b border-gray-200 p-6 flex-shrink-0 shadow-lg"
              style={{ height: `${videoPanelHeight}px` }}
            >
              <div className="relative h-full bg-white rounded-xl shadow-xl overflow-hidden">
                <button
                  onClick={() => setCurrentVideo(null)}
                  className="absolute top-4 right-4 z-20 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white p-2 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <X className="w-4 h-4" />
                </button>
                <div
                  className="w-full h-full flex"
                >
                  {/* Video Section */}
                  <div className="w-1/2 h-full bg-black flex items-center justify-center relative">
                    <video
                      ref={videoPlayerRef}
                      controls
                      className="w-full h-full object-contain"
                      style={{ backgroundColor: '#000' }}
                    >
                      Your browser does not support the video tag.
                    </video>
                    {/* Video overlay for better controls visibility */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none"></div>
                  </div>

                  {/* Transcript Section */}
                  <div
                    className="w-1/2 h-full flex flex-col bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50"
                  >
                    {/* Header */}
                    <div className="flex-shrink-0 px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        <h3 className="text-md font-bold tracking-wide">Transcripts</h3>
                      </div>
                      {currentVideo && (
                        <p className="text-indigo-100 text-sm mt-1 font-medium">
                          {currentVideo.replace(/_/g, ' ')}
                        </p>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-6 overflow-y-auto">
                      {isLoadingTranscript ? (
                        <div className="flex flex-col justify-center items-center h-full space-y-4">
                          <div className="relative">
                            <div className="animate-spin rounded-full h-12 w-12 border-4 border-indigo-200"></div>
                            <div className="animate-spin rounded-full h-12 w-12 border-4 border-indigo-600 border-t-transparent absolute top-0"></div>
                          </div>
                          <p className="text-indigo-600 font-medium">Loading transcript...</p>
                        </div>
                      ) : currentMessageContext ? (
                        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/50">
                          <div className="text-sm text-gray-800 whitespace-pre-wrap leading-relaxed font-medium">
                            {currentMessageContext}
                          </div>
                        </div>
                      ) : currentVideo ? (
                        <div className="flex flex-col justify-center items-center h-full space-y-4">
                          <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">No transcript available</p>
                            <p className="text-gray-500 text-sm mt-1">This video doesn't have a transcript yet.</p>
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col justify-center items-center h-full space-y-4">
                          <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">Select a video</p>
                            <p className="text-gray-500 text-sm mt-1">Click on a timestamp to view its transcript.</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Draggable Divider */}
            <div
              className={`
                h-2 bg-gradient-to-r from-indigo-300 via-purple-300 to-indigo-300 hover:from-indigo-400 hover:via-purple-400 hover:to-indigo-400 cursor-ns-resize transition-all duration-200 shadow-sm
                ${isDragging ? 'from-indigo-500 via-purple-500 to-indigo-500 shadow-md' : ''}
              `}
              onMouseDown={handleMouseDown}
            >
              <div className="flex justify-center items-center h-full">
                <div className="w-8 h-0.5 bg-white/50 rounded-full"></div>
              </div>
            </div>
          </>
        )}

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6 bg-gray-50">
          {activeChat.messages.map((msg) => (
            <MessageWithAudio
              key={msg.id}
              message={msg}
              context={msg.context.join(', ')}
              onToggleAudio={toggleAudio}
              onTimestampClick={handleTimestampClick}
            />
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="bg-white p-6 border-t border-gray-200">
          <div className="relative">
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={isTranscribing ? "Transcribing audio..." : "Send a message..."}
              rows={3}
              disabled={isTranscribing}
              className="
                w-full p-4 pr-16 rounded-xl border border-gray-300
                focus:outline-none focus:ring-2 focus:ring-indigo-500
                resize-none transition-all duration-300
              "
            />
            <div className="absolute bottom-5 right-5 flex items-center space-x-2">
              <button
                onClick={handleVoiceInput}
                disabled={isTranscribing}
                className={`
                  p-2 rounded-full transition-colors
                  ${isListening
                    ? 'bg-red-500 text-white animate-pulse'
                    : isTranscribing
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'hover:bg-gray-100 text-gray-500'
                  }
                `}
              >
                {isTranscribing
                  ? <Loader className="w-5 h-5 animate-spin" />
                  : <Mic className="w-5 h-5" />
                }
              </button>
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isProcessing || isTranscribing}
                className={`
                  bg-indigo-500 text-white p-2 rounded-full
                  hover:bg-indigo-600 transition-all duration-300
                  ${!inputMessage.trim() || isProcessing || isTranscribing
                    ? 'opacity-50 cursor-not-allowed'
                    : ''
                  }
                `}
              >
                {isProcessing ? <Loader className="w-5 h-5 animate-spin" /> : <Send className="w-5 h-5" />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal for entering identifier when creating a new chat */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setPendingNewChatId(null);
        }}
        onSubmit={handleModalSubmit}
        title="New Chat Context"
      />
    </div>
  );
};

export default ChatInterface;