from pyannote.audio import Pipeline
from pyannote.audio.pipelines.utils.hook import ProgressHook

# Login to Hugging Face Hub

# Initialize the pipeline
pipeline = Pipeline.from_pretrained(
    "pyannote/speaker-diarization-3.1", 
    use_auth_token="*************************************"
)

# Send pipeline to GPU (when available)
import torch
pipeline.to(torch.device("cuda"))

# Apply pretrained pipeline (with optional progress hook)
with ProgressHook() as hook:
    diarization = pipeline("brian.wav", hook=hook)

# Print the result
for turn, _, speaker in diarization.itertracks(yield_label=True):
    print(f"start={turn.start:.1f}s stop={turn.end:.1f}s speaker_{speaker}")