#include <iostream>

#pragma pack(1) // Disable padding
struct PackedExample {
    char a;   // 1 byte
    int b;    // 4 bytes
    float c;  // 4 bytes
}; // Total size: 1 + 4 + 4 = 9 bytes (no padding)
#pragma pack() // Reset packing to default

struct NormalExample {
    char a;   // 1 byte
    int b;    // 4 bytes
    float c;  // 4 bytes
}; // Likely size: 12 bytes due to padding

int main() {
    std::cout << "Size of NormalExample: " << sizeof(NormalExample) << " bytes" << std::endl;
    std::cout << "Size of PackedExample: " << sizeof(PackedExample) << " bytes" << std::endl;

    return 0;
}
