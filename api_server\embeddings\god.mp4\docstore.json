{"docstore/metadata": {"64904dc7-ffc6-4176-b64a-64f37d36015c": {"doc_hash": "a9fa4d0d1bce2f1dc8a516b5936fb9ab0d83d7cbc7a90ac6f508d494fa68f0fc"}, "ceff159b-7a59-45ce-930a-b7c2406056b4": {"doc_hash": "336390f869673fc93caf19d31ae008ef60097c19023e885e7c7405ccbf9b94ff"}, "ccb7c153-aac0-4f5d-9fab-c3008d172aef": {"doc_hash": "7e57206fe95b3731b94c0a71265ce769132ce4c9e113eb0c7e5bcfada26a47d6"}, "5d14a81b-912d-43db-89b5-65767a645e16": {"doc_hash": "0a053eb9db12d7c321c9e7e26d05d4171504e708cfe6ac7c93b06793295b2825"}, "9c33a253-8e22-49f6-a5a2-7b1e3ee11693": {"doc_hash": "2fe908711069d923a9ac52b9b2e378427315b24f1c874b0aa2fe2967dee6ff93"}, "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f": {"doc_hash": "480fcfc2a237e6ba77e2fff1ebabf05d4fb5ce330272919b624cf83dc5e25074"}, "0060c0ec-6d08-46a4-9e5f-d93ee3a38918": {"doc_hash": "6a2e25be111a47bb6bae3c5129e80849cda2092ffc07f5951c07f6a8da82eb8f"}, "94ad3d3c-c4a9-43a9-b53e-84b5338959d6": {"doc_hash": "830aab74de03c0828a98481cbb72deb5cbc23d2df6bcb07c7f07f9379c7c1e07"}, "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6": {"doc_hash": "c65c47d91c7d86a85ad8e8fc97834ebcb84551c0685952ccf84059faf7ec709f"}, "ee63a868-edaf-4917-b21d-39ece28a5cfe": {"doc_hash": "5fb38245dc6405b7af36ebda47abf4bafc417742a1334cb7c886cc1974ee35c4"}, "514c767c-3cf3-4211-ad03-ec27083bcbe1": {"doc_hash": "bbf925bd7428bba5ebbeb0463f2aa04e17c3d80945545021e1a77bf817ce6a1d"}, "8300d9e3-c22e-42fa-9af9-40bc12db4561": {"doc_hash": "c3f3ae6ac56de307c373f644d10a2dfdfb863aa1541a07e3724d8d0baba22cdf"}, "db55e714-59f7-460b-a5ae-1dd8ba3eb49c": {"doc_hash": "b6726b0de7a7cb65c8a31aebeea36c34e820569bab75518dc7f9ee6b58a4264e"}, "4739b662-c985-4a78-8365-3be3f81e809f": {"doc_hash": "112cf2c41b1e85acb4c3f9efa7214f83ad9c4cad253f587857bbbe21b74217c1"}, "fb105139-5e40-49b1-8d7b-ee9e319ab502": {"doc_hash": "04bef88b5f0dcaff0f1f1e55bb08395099257accad5806c999de817df90386d1"}, "c43f7f3b-0f8a-4bca-9f51-0245d03f0914": {"doc_hash": "058e4c8e74d62b468c090a559f11923b8291d2249dee5646df7668d434b49533"}, "5d402238-b90b-4b99-8961-fd787a2ec2e5": {"doc_hash": "904eb1ca5e8cbc9017747b0ffa8acc872e73a567681c9265506ba9d1e7ecd56a"}, "9bf8bbfc-97d1-4404-96af-2149fcb348f5": {"doc_hash": "6cb306b48dd708e5deb5b3abaafa45f1c4982f208e7cf7576ca213864b34f035"}, "5307fa86-e0b3-449d-bf87-44ba12e187af": {"doc_hash": "75652f5626d3a034b092bfb06b8b469a8cc8e8192c64daa9dbfa68c5bc1fd114"}, "cd545473-9fe9-4878-a453-7f72fd488cfc": {"doc_hash": "427c86b713e89ea116f9463b2a026e9bb592501861bb45c70c297ce3d7dc53dc"}, "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6": {"doc_hash": "398adcfe82df433050774ab48668f1483369eeb41352f55468c2daf162e8eefb"}, "c3eb66fd-dafa-492c-817b-b8468baaaf77": {"doc_hash": "e22c92de10c79ced1f48786f7245fcf31b574c60484e8fe401ef8b54c4ab6634"}, "b6cef169-aee3-4aa0-8668-6a0cf20e9278": {"doc_hash": "611977a7f35968dc31b638dc4ad4a8e586ab079f13855a5d4cbb0da874f60956"}, "3b85fa14-d5f7-4085-a462-4c5258f37880": {"doc_hash": "c430f211ec764bdcae76900c4e16697542b4c20511f8a6e85a9c4eb39b8909ac", "ref_doc_id": "64904dc7-ffc6-4176-b64a-64f37d36015c"}, "bab44c03-cbda-47bd-b746-1408ea025bf0": {"doc_hash": "4ad1c6bdd83bf205fa237e96a52302ed33f8398c587da5be9a1a8ded122b2e34", "ref_doc_id": "ceff159b-7a59-45ce-930a-b7c2406056b4"}, "78c4f0a6-c48b-464e-82a0-df2a9c13d38b": {"doc_hash": "9de25e42ac44b14202281b8dae5fe4ab91c9cd53c691e38b4d6cd11c3bd2905f", "ref_doc_id": "ccb7c153-aac0-4f5d-9fab-c3008d172aef"}, "aa787e6d-81a5-4b1d-8a33-7831331b1ac3": {"doc_hash": "1194a6613faaeb0a7a878895e81182794c6e44b2574f5abc3e763ef91df657c3", "ref_doc_id": "5d14a81b-912d-43db-89b5-65767a645e16"}, "19fe770b-3c12-411a-a760-c8e6f94025cd": {"doc_hash": "1266e39a5d4f42f55827c97085e7d7a15e0a65ada22276c3bcca0eef40680949", "ref_doc_id": "9c33a253-8e22-49f6-a5a2-7b1e3ee11693"}, "cbe64995-c12d-4e6f-a4c1-331001d42a31": {"doc_hash": "d6ef486336f72fd7c94bab14f62bae8101fbafdcb24b7ef6368225bb1ee3b1f5", "ref_doc_id": "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f"}, "46f7b82d-5fcd-49c3-a3b3-e22f5ffdbcf9": {"doc_hash": "1a62707754dbdba199310aa680aac7024b3e859ffee580073ab3558a663efd0e", "ref_doc_id": "0060c0ec-6d08-46a4-9e5f-d93ee3a38918"}, "1195bf20-a7e0-42d8-9d67-d22dddaa1f6b": {"doc_hash": "12b72f14b15db17716a930ec2b3d2a113630ee1670a79c3ec3b960847efe2cef", "ref_doc_id": "94ad3d3c-c4a9-43a9-b53e-84b5338959d6"}, "00bfc286-014e-42d1-8de7-79c3566472d4": {"doc_hash": "c5e28599e9280bda0b28bd4f44b88775f9cdbc7383329d3f4616ad07cf393d28", "ref_doc_id": "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6"}, "46e2a33a-04cf-4e9b-ad7f-f39f916b9db3": {"doc_hash": "0f2bfa49416af5c8f43ec021a10b7d6f4d6034a415f7e6ecc51d1b3fc196d572", "ref_doc_id": "ee63a868-edaf-4917-b21d-39ece28a5cfe"}, "38d221e3-bce7-4097-879b-ae6a774db8d3": {"doc_hash": "042d0c5661055c361ac474bc0a66398f9dfec1ef8821409527ffc8f3f7085115", "ref_doc_id": "514c767c-3cf3-4211-ad03-ec27083bcbe1"}, "06b12c1b-0532-4f30-bdde-5149c014a894": {"doc_hash": "6c0cf1da0adc53c56ad76abafc742a8c9588ea2126b6260b89ae31f41b3e4c0c", "ref_doc_id": "8300d9e3-c22e-42fa-9af9-40bc12db4561"}, "7fb893b7-1537-4919-a8ca-d0fdda1c246f": {"doc_hash": "6d60b2f115e243a490078036aa920d74db02d8d8b545de8ec301130953350590", "ref_doc_id": "db55e714-59f7-460b-a5ae-1dd8ba3eb49c"}, "b03f3569-d9c2-4136-80ab-59dc98b16fa3": {"doc_hash": "391414b3decc896a815cd84b2129c60099652efebfea5e52655bc5b95ffb32fe", "ref_doc_id": "4739b662-c985-4a78-8365-3be3f81e809f"}, "4f6b6417-0495-4538-9c80-246f6c82af94": {"doc_hash": "86c0d3bfa3c69ce49d4c066fe61e575cd6ea88d278f93e74b8d014a755157049", "ref_doc_id": "fb105139-5e40-49b1-8d7b-ee9e319ab502"}, "0af8fae4-edd4-4e09-b2b6-6766474fc6c5": {"doc_hash": "598a6095632b0b45dc8c5090d579165ed121d20727eecac8ae2dd46132c82c01", "ref_doc_id": "c43f7f3b-0f8a-4bca-9f51-0245d03f0914"}, "85d5082f-45fa-4db6-8f5c-d1b07622107f": {"doc_hash": "361862c0f977454a9199e3e48a15071f17d11e9701fc0d58e596a2aa9b2ccf82", "ref_doc_id": "5d402238-b90b-4b99-8961-fd787a2ec2e5"}, "8db1c00d-d884-469d-98dd-f31b62f82559": {"doc_hash": "4d7a1162cb93cc1ddf90f1cbaa63e493c4b99ea4340852f2366ffc3065e4dbd0", "ref_doc_id": "9bf8bbfc-97d1-4404-96af-2149fcb348f5"}, "38b1274d-47bb-4b8e-b821-e62fb5e9dd04": {"doc_hash": "ec88fbfdfbb20e86dbdb39d217a8df6b13bcffd567f28af97dfb2b88a33bdafe", "ref_doc_id": "5307fa86-e0b3-449d-bf87-44ba12e187af"}, "ff2a61db-2df0-4e5c-9ed0-404a7e87f62c": {"doc_hash": "4a7ff22f59b74ae00b39ab65c06df98010df828f0b0b4f73907cce1a5ff5e256", "ref_doc_id": "cd545473-9fe9-4878-a453-7f72fd488cfc"}, "128d3759-a252-4248-ad87-bce84243539d": {"doc_hash": "34f74bec1826454cdb578a2b93e65d0a6622adc986e579b2b90f9630bace0253", "ref_doc_id": "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6"}, "3249d85e-81a6-4b3b-a3c7-516160ef5a64": {"doc_hash": "79f3323b887004dcd12ecf4ed33ad6b6eba116e5683145bc59984c8eb1a643f7", "ref_doc_id": "c3eb66fd-dafa-492c-817b-b8468baaaf77"}, "c175ea08-7a86-4730-bce3-91174842b7ef": {"doc_hash": "60b15fc5021301e78ef4722e38e354aa6d5d85fabee94f96b2e71d1a70622fa1", "ref_doc_id": "b6cef169-aee3-4aa0-8668-6a0cf20e9278"}}, "docstore/ref_doc_info": {"64904dc7-ffc6-4176-b64a-64f37d36015c": {"node_ids": ["3b85fa14-d5f7-4085-a462-4c5258f37880"], "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}}, "ceff159b-7a59-45ce-930a-b7c2406056b4": {"node_ids": ["bab44c03-cbda-47bd-b746-1408ea025bf0"], "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}}, "ccb7c153-aac0-4f5d-9fab-c3008d172aef": {"node_ids": ["78c4f0a6-c48b-464e-82a0-df2a9c13d38b"], "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}}, "5d14a81b-912d-43db-89b5-65767a645e16": {"node_ids": ["aa787e6d-81a5-4b1d-8a33-7831331b1ac3"], "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}}, "9c33a253-8e22-49f6-a5a2-7b1e3ee11693": {"node_ids": ["19fe770b-3c12-411a-a760-c8e6f94025cd"], "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}}, "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f": {"node_ids": ["cbe64995-c12d-4e6f-a4c1-331001d42a31"], "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}}, "0060c0ec-6d08-46a4-9e5f-d93ee3a38918": {"node_ids": ["46f7b82d-5fcd-49c3-a3b3-e22f5ffdbcf9"], "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}}, "94ad3d3c-c4a9-43a9-b53e-84b5338959d6": {"node_ids": ["1195bf20-a7e0-42d8-9d67-d22dddaa1f6b"], "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}}, "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6": {"node_ids": ["00bfc286-014e-42d1-8de7-79c3566472d4"], "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}}, "ee63a868-edaf-4917-b21d-39ece28a5cfe": {"node_ids": ["46e2a33a-04cf-4e9b-ad7f-f39f916b9db3"], "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}}, "514c767c-3cf3-4211-ad03-ec27083bcbe1": {"node_ids": ["38d221e3-bce7-4097-879b-ae6a774db8d3"], "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}}, "8300d9e3-c22e-42fa-9af9-40bc12db4561": {"node_ids": ["06b12c1b-0532-4f30-bdde-5149c014a894"], "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}}, "db55e714-59f7-460b-a5ae-1dd8ba3eb49c": {"node_ids": ["7fb893b7-1537-4919-a8ca-d0fdda1c246f"], "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}}, "4739b662-c985-4a78-8365-3be3f81e809f": {"node_ids": ["b03f3569-d9c2-4136-80ab-59dc98b16fa3"], "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}}, "fb105139-5e40-49b1-8d7b-ee9e319ab502": {"node_ids": ["4f6b6417-0495-4538-9c80-246f6c82af94"], "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}}, "c43f7f3b-0f8a-4bca-9f51-0245d03f0914": {"node_ids": ["0af8fae4-edd4-4e09-b2b6-6766474fc6c5"], "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}}, "5d402238-b90b-4b99-8961-fd787a2ec2e5": {"node_ids": ["85d5082f-45fa-4db6-8f5c-d1b07622107f"], "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}}, "9bf8bbfc-97d1-4404-96af-2149fcb348f5": {"node_ids": ["8db1c00d-d884-469d-98dd-f31b62f82559"], "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}}, "5307fa86-e0b3-449d-bf87-44ba12e187af": {"node_ids": ["38b1274d-47bb-4b8e-b821-e62fb5e9dd04"], "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}}, "cd545473-9fe9-4878-a453-7f72fd488cfc": {"node_ids": ["ff2a61db-2df0-4e5c-9ed0-404a7e87f62c"], "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}}, "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6": {"node_ids": ["128d3759-a252-4248-ad87-bce84243539d"], "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}}, "c3eb66fd-dafa-492c-817b-b8468baaaf77": {"node_ids": ["3249d85e-81a6-4b3b-a3c7-516160ef5a64"], "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}}, "b6cef169-aee3-4aa0-8668-6a0cf20e9278": {"node_ids": ["c175ea08-7a86-4730-bce3-91174842b7ef"], "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}}}, "docstore/data": {"3b85fa14-d5f7-4085-a462-4c5258f37880": {"__data__": {"id_": "3b85fa14-d5f7-4085-a462-4c5258f37880", "embedding": null, "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "64904dc7-ffc6-4176-b64a-64f37d36015c", "node_type": "4", "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "a9fa4d0d1bce2f1dc8a516b5936fb9ab0d83d7cbc7a90ac6f508d494fa68f0fc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Hello friends, I am going to start a course on algorithms. Algorithm as a subject. Algorithm is a common subject for computer science engineering students. Most of the universities offer this course as a part of syllabus and this is a very core subject and very important subject. And students face some difficulties in some of the topics in this one. They could not understand them very clearly.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 396, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "bab44c03-cbda-47bd-b746-1408ea025bf0": {"__data__": {"id_": "bab44c03-cbda-47bd-b746-1408ea025bf0", "embedding": null, "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ceff159b-7a59-45ce-930a-b7c2406056b4", "node_type": "4", "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "336390f869673fc93caf19d31ae008ef60097c19023e885e7c7405ccbf9b94ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So here I am going to make the subject more and more simple so that you can easily understand and even you can practice that and also remember it easily. The importance of the subject is that apart from theoretical examination, it is also important for competitive exams. And even programming contest, most of them are designed from this subject only. If any programming challenge is there, then mostly they pick up the questions from this subject.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 448, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "78c4f0a6-c48b-464e-82a0-df2a9c13d38b": {"__data__": {"id_": "78c4f0a6-c48b-464e-82a0-df2a9c13d38b", "embedding": null, "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ccb7c153-aac0-4f5d-9fab-c3008d172aef", "node_type": "4", "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "hash": "7e57206fe95b3731b94c0a71265ce769132ce4c9e113eb0c7e5bcfada26a47d6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So this is one of the challenging subject for the student because they get a chance to solve different type of problems and they get the strategy or approach for solving the problem. So some students fail to understand it properly so they could not get the approach or the strategy for solving the problems and they feel that they are lacking in the logic development or strategic development of a program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 406, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "aa787e6d-81a5-4b1d-8a33-7831331b1ac3": {"__data__": {"id_": "aa787e6d-81a5-4b1d-8a33-7831331b1ac3", "embedding": null, "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5d14a81b-912d-43db-89b5-65767a645e16", "node_type": "4", "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "hash": "0a053eb9db12d7c321c9e7e26d05d4171504e708cfe6ac7c93b06793295b2825", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So I will be covering in depth each and everything right from the basics to the depth. So that you will be able to answer any type of question from this one. I will cover the topics such that just you look at the question you can get the answer that I guarantee you. So the thing is I will cover those points which are useful for solving the questions.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 352, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "19fe770b-3c12-411a-a760-c8e6f94025cd": {"__data__": {"id_": "19fe770b-3c12-411a-a760-c8e6f94025cd", "embedding": null, "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9c33a253-8e22-49f6-a5a2-7b1e3ee11693", "node_type": "4", "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "hash": "2fe908711069d923a9ac52b9b2e378427315b24f1c874b0aa2fe2967dee6ff93", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Some job interviews or even for any other exam or some entrance exams like gate exam, the cushions are framed from algorithms also. There is also one of the key subject there. So you will be able to answer any type of question from there. Now towards the lectures in the order, I'll be giving the numbers for each topic so that you follow that order, you follow the sequence because I may be breaking the major topic into small topics.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 435, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "cbe64995-c12d-4e6f-a4c1-331001d42a31": {"__data__": {"id_": "cbe64995-c12d-4e6f-a4c1-331001d42a31", "embedding": null, "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f", "node_type": "4", "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "480fcfc2a237e6ba77e2fff1ebabf05d4fb5ce330272919b624cf83dc5e25074", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And one more important thing is your feedback is very important for me because already I have made few prepared few videos using presentations and other tools but now I am giving a lecture on whiteboard so maybe I am new for the video quality and audio quality all these things so technically once I get set up so I need your feedback so I can make my videos more and more better", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 379, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "46f7b82d-5fcd-49c3-a3b3-e22f5ffdbcf9": {"__data__": {"id_": "46f7b82d-5fcd-49c3-a3b3-e22f5ffdbcf9", "embedding": null, "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0060c0ec-6d08-46a4-9e5f-d93ee3a38918", "node_type": "4", "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "hash": "6a2e25be111a47bb6bae3c5129e80849cda2092ffc07f5951c07f6a8da82eb8f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So your feedback is important for me to know how the video quality and audio quality you are getting it on your devices because I have checked on different devices and I found that the results are different. So what devices you are using, what I should improve, voice or what so I need your feedback. Let us start with just introduction. What is an Elcortone?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 359, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1195bf20-a7e0-42d8-9d67-d22dddaa1f6b": {"__data__": {"id_": "1195bf20-a7e0-42d8-9d67-d22dddaa1f6b", "embedding": null, "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "94ad3d3c-c4a9-43a9-b53e-84b5338959d6", "node_type": "4", "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "hash": "830aab74de03c0828a98481cbb72deb5cbc23d2df6bcb07c7f07f9379c7c1e07", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "What is an Elgortum? Why it is written? When it is written? Who will write it? Let us get the answers for these. See, algorithm as a definition, it's a common definition everybody knows that, it's a step by step procedure for solving a computational problem. Yes, it is a step by step procedure for solving a computational problem. Then what is a program? Program is also a step by step procedure for solving a problem. Then what is the difference between program and Elgortum?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 477, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "00bfc286-014e-42d1-8de7-79c3566472d4": {"__data__": {"id_": "00bfc286-014e-42d1-8de7-79c3566472d4", "embedding": null, "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6", "node_type": "4", "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "hash": "c65c47d91c7d86a85ad8e8fc97834ebcb84551c0685952ccf84059faf7ec709f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So by comparing program and algorithm I can make you understand the importance of algorithm, meaning of algorithm. Let us take it. Here is an algorithm and here is a program. Let us see the first thing, first difference. If you know the software development life cycle, means the phases of developing a software project. In the phases of development of software project there are two important phases.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 401, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "46e2a33a-04cf-4e9b-ad7f-f39f916b9db3": {"__data__": {"id_": "46e2a33a-04cf-4e9b-ad7f-f39f916b9db3", "embedding": null, "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ee63a868-edaf-4917-b21d-39ece28a5cfe", "node_type": "4", "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "5fb38245dc6405b7af36ebda47abf4bafc417742a1334cb7c886cc1974ee35c4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "design phase and the implementation phase. Now if you are not aware of it let me tell you whatever you want to manufacture or construct something by engineering procedures then first step one of the important step is designing. First you do designing make your design perfect and thorough so that you can understand what you are going to construct what you are going to develop and when you are sure what you are going to develop then you start to develop.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 456, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "38d221e3-bce7-4097-879b-ae6a774db8d3": {"__data__": {"id_": "38d221e3-bce7-4097-879b-ae6a774db8d3", "embedding": null, "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "514c767c-3cf3-4211-ad03-ec27083bcbe1", "node_type": "4", "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "bbf925bd7428bba5ebbeb0463f2aa04e17c3d80945545021e1a77bf817ce6a1d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can't develop anything, you cannot construct anything on trial and error basis. Like you constructed something, again no one is wrong, destroyed and again created a new one. So no. But it is easy in software engineering. Software engineer can write some program and change the mind, delete the program and again start writing them. So that's why we cannot get the feel that we have wasted so much amount of time in writing so useless program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "06b12c1b-0532-4f30-bdde-5149c014a894": {"__data__": {"id_": "06b12c1b-0532-4f30-bdde-5149c014a894", "embedding": null, "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8300d9e3-c22e-42fa-9af9-40bc12db4561", "node_type": "4", "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "hash": "c3f3ae6ac56de307c373f644d10a2dfdfb863aa1541a07e3724d8d0baba22cdf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So the point is first you design and then you write the program. So at the design time what do you use? So you are not writing a program then what do you write? So that same program we write it in simple English like statements that are easy to understand without using proper syntax and we may not be writing on machine that is on computer, we may be writing on paper.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 369, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7fb893b7-1537-4919-a8ca-d0fdda1c246f": {"__data__": {"id_": "7fb893b7-1537-4919-a8ca-d0fdda1c246f", "embedding": null, "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "db55e714-59f7-460b-a5ae-1dd8ba3eb49c", "node_type": "4", "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "hash": "b6726b0de7a7cb65c8a31aebeea36c34e820569bab75518dc7f9ee6b58a4264e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "or even if you are using a machine also then you will not be writing in some language, you will be writing in MS Word or notepad like application. So just you are getting familiar how your program is going to work. So that is nothing but an algorithm. So algorithms are written at design time and when the programs are written they are written at implementation time, design time and implementation.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 399, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b03f3569-d9c2-4136-80ab-59dc98b16fa3": {"__data__": {"id_": "b03f3569-d9c2-4136-80ab-59dc98b16fa3", "embedding": null, "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4739b662-c985-4a78-8365-3be3f81e809f", "node_type": "4", "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "112cf2c41b1e85acb4c3f9efa7214f83ad9c4cad253f587857bbbe21b74217c1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So first you make a design what your procedure is going to do, what your software is going to do, come up with some design and that design you convert into a program. Then what do you call the person who do designing? Whether the programmer does that? Yes if programmer is capable of doing that he can do it otherwise the person who will do this one should have the domain knowledge, the problems knowledge, knowledge about the problem and its solution.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 453, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4f6b6417-0495-4538-9c80-246f6c82af94": {"__data__": {"id_": "4f6b6417-0495-4538-9c80-246f6c82af94", "embedding": null, "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fb105139-5e40-49b1-8d7b-ee9e319ab502", "node_type": "4", "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "04bef88b5f0dcaff0f1f1e55bb08395099257accad5806c999de817df90386d1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He can give a solution, the one who understands the problem and has the domain knowledge. Suppose you are developing a software or an algorithm or a program for account then accountant can understand accounting better. Or if you are writing an application or developing an application for hospital then doctors or the administrative staff of a hospital can understand the system better than a programmer.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 404, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0af8fae4-edd4-4e09-b2b6-6766474fc6c5": {"__data__": {"id_": "0af8fae4-edd4-4e09-b2b6-6766474fc6c5", "embedding": null, "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c43f7f3b-0f8a-4bca-9f51-0245d03f0914", "node_type": "4", "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "hash": "058e4c8e74d62b468c090a559f11923b8291d2249dee5646df7668d434b49533", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So mostly the one who is having a knowledge about the domain for which the application is being developed, they can write on the algorithm. So the person who writes should have domain knowledge. Who will write the programmer? Programmer? So programmers can also have domain knowledge. They can also have domain knowledge that he is acting as a designer and here he is acting as a programmer. Next, what is the language used for writing algorithm? You can use any language.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 472, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "85d5082f-45fa-4db6-8f5c-d1b07622107f": {"__data__": {"id_": "85d5082f-45fa-4db6-8f5c-d1b07622107f", "embedding": null, "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5d402238-b90b-4b99-8961-fd787a2ec2e5", "node_type": "4", "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "hash": "904eb1ca5e8cbc9017747b0ffa8acc872e73a567681c9265506ba9d1e7ecd56a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Any language, any language means English like language or you can use some mathematical notations. If you're writing English like language it will be like writing paras or samari but don't try it. If you want you can use mathematical notations also. It's better if you use mathematical notations. So any language can be used, any language means English language or some mathematical notations can be used as long as it is understandable by those people who are using it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 470, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8db1c00d-d884-469d-98dd-f31b62f82559": {"__data__": {"id_": "8db1c00d-d884-469d-98dd-f31b62f82559", "embedding": null, "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9bf8bbfc-97d1-4404-96af-2149fcb348f5", "node_type": "4", "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "hash": "6cb306b48dd708e5deb5b3abaafa45f1c4982f208e7cf7576ca213864b34f035", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Like if a designer has written an algorithm then the programmer will be writing a program for it. So designers should understand and also programmers should understand that one. So the team of programmers who are working on that project should be able to understand it. Then this is written only using programming language like C++, Java, Python. So you can use different languages for developing a program. The next one more important thing here", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "38b1274d-47bb-4b8e-b821-e62fb5e9dd04": {"__data__": {"id_": "38b1274d-47bb-4b8e-b821-e62fb5e9dd04", "embedding": null, "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5307fa86-e0b3-449d-bf87-44ba12e187af", "node_type": "4", "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "hash": "75652f5626d3a034b092bfb06b8b469a8cc8e8192c64daa9dbfa68c5bc1fd114", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "When you write an L-Core, it's going to be hardware and software means operating system independent. It's not dependent on hardware, what machine you are going to use, what's the configuration of the machine and what's the operating system, either Linux operating system or Windows operating system, we don't bother about it. But when you write a program, it is dependent on hardware and operating system.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 405, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ff2a61db-2df0-4e5c-9ed0-404a7e87f62c": {"__data__": {"id_": "ff2a61db-2df0-4e5c-9ed0-404a7e87f62c", "embedding": null, "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cd545473-9fe9-4878-a453-7f72fd488cfc", "node_type": "4", "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "427c86b713e89ea116f9463b2a026e9bb592501861bb45c70c297ce3d7dc53dc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So when you develop the program you have to select some hardware on which you are going to run and also you have to select some operating system. You may be doing it for Linux or you may be doing it for Windows. So the method will be different and the environments are different. So this is important.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 301, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "128d3759-a252-4248-ad87-bce84243539d": {"__data__": {"id_": "128d3759-a252-4248-ad87-bce84243539d", "embedding": null, "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6", "node_type": "4", "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "hash": "398adcfe82df433050774ab48668f1483369eeb41352f55468c2daf162e8eefb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Then next, after writing an algorithm we analyze, analyze an algorithm. Means we will study the algorithm to find out are we achieving the results perfectly or not and our algorithm is efficient or not in terms of time and space. We will see what does it mean by analysis. So we will analyze an algorithm. What do we do with the program? So you don't have to study the program. All the program is there. Just run it and check it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 429, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3249d85e-81a6-4b3b-a3c7-516160ef5a64": {"__data__": {"id_": "3249d85e-81a6-4b3b-a3c7-516160ef5a64", "embedding": null, "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c3eb66fd-dafa-492c-817b-b8468baaaf77", "node_type": "4", "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "hash": "e22c92de10c79ced1f48786f7245fcf31b574c60484e8fe401ef8b54c4ab6634", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So we do testing, testing of the program. That's it. These are few differences between algorithm and program. These differences will help you understand what are algorithms. All right? Now one more thing I will tell you. The syntax of the language that we use, any language can be used but who actually write algorithms and who use them and who write the programs of programmers who are coming from university", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 409, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c175ea08-7a86-4730-bce3-91174842b7ef": {"__data__": {"id_": "c175ea08-7a86-4730-bce3-91174842b7ef", "embedding": null, "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b6cef169-aee3-4aa0-8668-6a0cf20e9278", "node_type": "4", "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "hash": "611977a7f35968dc31b638dc4ad4a8e586ab079f13855a5d4cbb0da874f60956", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "mostly university graduate knows at least C language. So nowadays mostly in the name of algorithm we write C language program only. So the benefit is that the advantage that everybody knows C language now. So instead of writing some other language to confuse we can use C language only so that everybody can understand. So even at a school level C language is being taught so everybody is familiar with C language. So I may be using C language syntax for writing algorithm.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 473, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}