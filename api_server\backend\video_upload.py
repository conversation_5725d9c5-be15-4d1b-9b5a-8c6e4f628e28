import os
import json
import yt_dlp
from models import whisper_model
from fastapi import APIRouter, UploadFile, File

router = APIRouter()

UPLOAD_DIR = "uploads"
TRANSCRIPTS_DIR = "transcriptions/"
DOWNLOAD_DIR = "downloads/"

# Ensure required directories exist
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(TRANSCRIPTS_DIR, exist_ok=True)
os.makedirs(DOWNLOAD_DIR, exist_ok=True)

import yt_dlp

def transcribe_youtube_video(video_url):
    ydl_opts = {
        'ffmpeg_location': 'C:\\ffmpeg\\bin',  # Use the directory, not the .exe file
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }],
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([video_url])

# Test function


def save_uploaded_file(upload_file: UploadFile):
    """Save the uploaded file in chunks to prevent high memory usage."""
    file_path = os.path.join(UPLOAD_DIR, upload_file.filename)

    with open(file_path, "wb") as buffer:
        for chunk in iter(lambda: upload_file.file.read(1024 * 1024), b""):  # Read in 1MB chunks
            buffer.write(chunk)

    return file_path  # Return saved file path

@router.post("/upload")
async def upload_video(file: UploadFile = File(...)):
    """Handles video file uploads."""
    file_path = save_uploaded_file(file)  
    return {"message": "File uploaded successfully", "file_path": file_path}

def download_youtube_video(youtube_url: str):
    """Download YouTube video as audio and return the file path."""
    ydl_opts = {
        "format": "bestaudio/best",
        "outtmpl": os.path.join(DOWNLOAD_DIR, "%(title)s.%(ext)s"),
        "postprocessors": [{"key": "FFmpegExtractAudio", "preferredcodec": "mp3"}],
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info_dict = ydl.extract_info(youtube_url, download=True)
        title = info_dict["title"]
        audio_path = os.path.join(DOWNLOAD_DIR, f"{title}.mp3")  # Force .mp3 extension
        return audio_path, title

def transcribe_youtube_video(youtube_url: str):
    """Download YouTube video, transcribe it, and store timestamps."""
    audio_path, title = download_youtube_video(youtube_url)

    # Run Whisper transcription
    segments, _ = whisper_model.transcribe(audio_path)

    transcript_data = [
        {"start": segment.start, "end": segment.end, "text": segment.text} for segment in segments
    ]

    # Save transcript as JSON
    transcript_path = os.path.join(TRANSCRIPTS_DIR, f"{title}.json")
    with open(transcript_path, "w") as f:
        json.dump(transcript_data, f, indent=4)

    return {"message": f"Transcription completed for {title}!", "transcript_file": transcript_path}