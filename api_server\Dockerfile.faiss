# Use Python 3.9 slim image for FAISS backend
FROM python:3.9-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements file first to leverage Docker cache
COPY faiss_backend_requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r faiss_backend_requirements.txt

# Create necessary directories
RUN mkdir -p embeddings merged_embeddings chat_mappings

# Copy the FAISS backend application code
COPY faiss_backend.py .
COPY new_faiss_backend.py .

# Expose the port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Command to run the FAISS backend application
CMD ["uvicorn", "faiss_backend:app", "--host", "0.0.0.0", "--port", "8001"]
