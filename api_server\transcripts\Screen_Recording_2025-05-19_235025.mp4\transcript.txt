Hello everyone, myself <PERSON><PERSON>, main my friend <PERSON><PERSON> the way he is going to explain about computer absolute photography. Computer absolute photography or also known as CL is a volumetric 3D printing company developed by the researchers at UC Berkeley. It's not like general 3D printing techniques as general techniques is layer by layer printing whereas in CL we use light projections to create and 3D objects on the resume.
It takes inspiration from CT standing or CT imaging. But, in CT standing we try to reproduce 2D X-ray images from a 3D object. In CAL we use multiple 2D images of an object at different angles and try to create a 3D projection of 3D object from it. It uses the four principles of tomography and polypulmonaryization together.
And he's a step by step procedure followed during the CAL for printing. He'll go through one by one. Then comes to step one, it's called 3D model design. We started with an CAD model of the object. Model is this list slides into 2D projections from multiple angles. Algorithms like a random transaction, transform are used to generate the slices.
And this slice is not typical layer used in VM or SLA. The light projection patterns used in CAL determine how the object will be built inside the resin volume. Step 2 is also called as a light pattern calculation. We create a sequence of 2D light images based on model slices. Each image corresponds to specific rotation angle. Light intensity is computed to ensure proper relative exposure.
These patterns are designed so that when projected and combined during rotation, the resin resets enough cumulative light only in the desired regions. Light intensity at each angle is carefully controlled to prevent overexposure on the cutting. Step 3 is called as resin preparation. You fill and transfer in cylindrical control with photopolymerization resin. The resin should cure only about a specific light threshold.
mount container, you know, you put the mount container on a rotating platform. The key requirements for fail resins for our solute photopolymer is reasonable, oxygen inhibited, low light scattering and low viscosity and comes to step four. We feed the DLQ projector with the frames of images and the DLQ projector projects the 2D images while the resin and the
container, it's rotated. Each image is particular specific angle synchronization rotation and projection ensure a preserved exposure. The step is crucial. It allows lies to build up at the right places across the full volume of the region and from here my friend Arishi will be taking over. Thank you.