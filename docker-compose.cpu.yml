services:
  # Main API Server (Transcription, TTS, Video Processing) - CPU Only
  server:
    build:
      context: ./api_server
      dockerfile: Dockerfile.cpu
    container_name: hpe-server-cpu
    ports:
      - "8000:8000"
    volumes:
      - ./api_server/transcripts:/app/transcripts
      - ./api_server/videos:/app/videos
      - ./api_server/audio_output:/app/audio_output
      - ./api_server/audio_only:/app/audio_only
    environment:
      - PYTHONUNBUFFERED=1
      - BACKEND_EMBED_URL=http://faiss_backend:8001/embed
      - GROQ_API_KEY=${GROQ_API_KEY}
    depends_on:
      - faiss_backend
    networks:
      - hpe-network
    restart: "no"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 40s

  # FAISS Backend (Embeddings and Chat)
  faiss_backend:
    build:
      context: ./api_server
      dockerfile: Dockerfile.faiss
    container_name: hpe-faiss-backend
    ports:
      - "8001:8001"
    volumes:
      - ./api_server/transcripts:/app/transcripts
      - ./api_server/embeddings:/app/embeddings
      - ./api_server/merged_embeddings:/app/merged_embeddings
      - ./api_server/chat_mappings:/app/chat_mappings
    environment:
      - PYTHONUNBUFFERED=1
      - GROQ_API_KEY=${GROQ_API_KEY}
    networks:
      - hpe-network
    restart: "no"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend (React/Vite Application)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: hpe-frontend
    ports:
      - "80:80"
    depends_on:
      - server
      - faiss_backend
    networks:
      - hpe-network
    restart: "no"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  hpe-network:
    driver: bridge

volumes:
  transcripts:
  videos:
  audio_output:
  embeddings:
  merged_embeddings:
  chat_mappings:
