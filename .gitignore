# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
api_server/.env/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker volumes and runtime data (exclude user data)
api_server/transcripts/
api_server/videos/
api_server/audio_output/
api_server/embeddings/
api_server/merged_embeddings/
api_server/chat_mappings/
api_server/audio_only/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Frontend build
frontend/dist/
frontend/build/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Model files (if large)
*.bin
*.model
*.pt
*.pth

# API keys and secrets
.env.local
.env.production
secrets/
