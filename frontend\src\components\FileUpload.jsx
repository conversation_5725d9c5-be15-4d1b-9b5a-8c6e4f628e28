import React, { useState, useRef, useEffect } from 'react';
import { CloudUploadIcon, LinkIcon, XIcon, Tag, ChevronDown } from 'lucide-react';
import axios from 'axios';

const FileUpload = ({ onUploadComplete, speech_len }) => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [dragActive, setDragActive] = useState(false);
  const [videoLink, setVideoLink] = useState('');
  const [activeTab, setActiveTab] = useState('upload');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const inputRef = useRef(null);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files) {
      const newFiles = Array.from(e.dataTransfer.files);
      setSelectedFiles(prev => [...prev, ...newFiles]);
    }
  };

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setSelectedFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeFile = (fileName) => {
    setSelectedFiles(prev => prev.filter(file => file.name !== fileName));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);

    try {
      if (selectedFiles.length > 0) {
        setIsUploading(true);

        // Array to collect all transcription results
        const allResults = [];
        const totalFiles = selectedFiles.length;

        // Process each file sequentially
        for (let i = 0; i < selectedFiles.length; i++) {
          const file = selectedFiles[i];
          const formData = new FormData();
          formData.append('file', file);
          formData.append('speech_len', String(speech_len));

          // Generate timestamp for filename_timestamp identifier
          const timestamp = new Date().toISOString().replace(/[-:.]/g, '');
          const fileIdentifier = `${timestamp}_${file.name.replace(/\s+/g, '_')}`;
          formData.append('context_id', fileIdentifier);

          // Update progress info to include file number
          console.log(`Submitting file ${i+1}/${totalFiles}: ${file.name}, Identifier: ${fileIdentifier}`);

          // Upload with progress tracking
          const response = await axios.post('http://localhost:8000/transcribe/', formData, {
            onUploadProgress: (progressEvent) => {
              // Calculate overall progress considering all files
              const fileProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              const overallProgress = Math.round((i * 100 + fileProgress) / totalFiles);
              setUploadProgress(overallProgress);
            }
          });

          // Add result to collection
          allResults.push(response.data);
          console.log(`File ${i+1}/${totalFiles} transcription completed:`, response.data);
        }

        console.log('All files transcription completed:', allResults);

        // If transcription was successful, return all results
        if (onUploadComplete) {
          onUploadComplete(allResults);
        }

      } else if (videoLink && validateVideoLink(videoLink)) {
        console.log('Submitting video link:', videoLink);
        setIsUploading(true);

        try {
          // Generate timestamp for filename_timestamp identifier
          const timestamp = new Date().toISOString().replace(/[-:.]/g, '');
          const videoId = extractVideoId(videoLink) || 'video';
          const linkIdentifier = `${timestamp}_youtube_${videoId}`;

          // Send the link to the backend for processing
          const response = await axios.post('http://localhost:8000/link_transcript/', {
            url: videoLink,
            context_id: linkIdentifier,
          });

          console.log('Link transcription completed:', response.data);

          // If transcription was successful, return the result
          if (onUploadComplete) {
            onUploadComplete([response.data]);
          }
        } catch (linkError) {
          console.error('Link transcription failed:', linkError);
          setError('Link transcription failed. Please try again.');
        } finally {
          setIsUploading(false);
        }
      }
    } catch (err) {
      console.error('Upload or transcription failed:', err);
      setError('Upload or transcription failed. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const validateVideoLink = (link) => {
    // Improved URL validation that handles various YouTube URL formats
    // This regex matches standard URLs and YouTube-specific formats
    const urlPattern = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/)|youtu\.be\/)[a-zA-Z0-9_-]+(\?.*)?$/;

    // More general URL validation as a fallback
    const generalUrlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;

    return urlPattern.test(link) || generalUrlPattern.test(link);
  };

  // Helper function to extract video ID from YouTube URL
  const extractVideoId = (url) => {
    const patterns = [
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i,
      /youtube\.com\/watch\?v=([^&]+)/i,
      /youtu\.be\/([^?]+)/i
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return null;
  };



  return (
    <div className="max-w-xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="border-b border-gray-200">
        <div className="flex">
          <button
            onClick={() => setActiveTab('upload')}
            className={`flex-1 py-3 px-4 flex items-center justify-center focus:outline-none ${
              activeTab === 'upload'
                ? 'bg-indigo-50 text-indigo-600 border-b-2 border-indigo-500'
                : 'text-gray-500 hover:bg-gray-50'
            }`}
          >
            <CloudUploadIcon className="mr-2 h-5 w-5" />
            File Upload
          </button>
          <button
            onClick={() => setActiveTab('link')}
            className={`flex-1 py-3 px-4 flex items-center justify-center focus:outline-none ${
              activeTab === 'link'
                ? 'bg-indigo-50 text-indigo-600 border-b-2 border-indigo-500'
                : 'text-gray-500 hover:bg-gray-50'
            }`}
          >
            <LinkIcon className="mr-2 h-5 w-5" />
            Video Link
          </button>
        </div>
      </div>

      <div className="p-6">
        {activeTab === 'upload' && (
          <form
            onDragEnter={handleDrag}
            onSubmit={handleSubmit}
            className={`relative p-6 border-2 border-dashed rounded-lg text-center
              ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}`}
          >
            <input
              ref={inputRef}
              type="file"
              multiple
              onChange={handleChange}
              className="hidden"
              accept=".mp4,.avi,.mov,.mkv,.mp3,.wav,.flac"
            />

            <div className="flex flex-col items-center justify-center space-y-4">
              <CloudUploadIcon className="w-16 h-16 text-gray-400" />
              <p className="text-gray-600">
                Drag & drop your video or audio files here
              </p>
              <button
                type="button"
                onClick={() => inputRef.current.click()}
                className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors"
              >
                Browse Files
              </button>
            </div>
            {dragActive &&
              <div
                className="absolute inset-0 z-50"
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              ></div>
            }
          </form>
        )}

        {activeTab === 'link' && (
          <div className="space-y-4">
            <div className="flex space-x-2">
              <input
                type="text"
                placeholder="Paste your YouTube or video link here"
                value={videoLink}
                onChange={(e) => setVideoLink(e.target.value)}
                className={`flex-grow px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                  videoLink
                    ? (validateVideoLink(videoLink)
                      ? 'border-green-500 focus:ring-green-200'
                      : 'border-red-500 focus:ring-red-200')
                    : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-200'
                }`}
              />
              {videoLink && (
                <button
                  onClick={() => setVideoLink('')}
                  className="p-2 text-gray-400 hover:text-gray-600"
                  title="Clear input"
                >
                  <XIcon className="h-5 w-5" />
                </button>
              )}
            </div>
            {videoLink && !validateVideoLink(videoLink) && (
              <p className="text-sm text-red-500">Please enter a valid URL</p>
            )}
            {videoLink && validateVideoLink(videoLink) && videoLink.includes('youtube') && (
              <div className="mt-2">
                <p className="text-sm text-green-600">Valid YouTube URL</p>
                {/*                {extractVideoId(videoLink) && (
                  <div className="mt-2 bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Preview:</p>
                    <div className="aspect-w-16 aspect-h-9 mt-1">
                      <img
                        src={`https://img.youtube.com/vi/${extractVideoId(videoLink)}/hqdefault.jpg`}
                        alt="YouTube Thumbnail"
                        className="rounded-md object-cover w-full h-24"
                      />
                    </div>
                  </div>
                )}*/}
              </div>
            )}
          </div>
        )}



        {/* Selected Files List */}
        {selectedFiles.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Selected Files:</h3>
            <ul className="space-y-2">
              {selectedFiles.map((file, index) => (
                <li key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded-md">
                  <div className="flex items-center">
                    <span className="text-sm text-gray-700 truncate max-w-xs">{file.name}</span>
                    <span className="ml-2 text-xs text-gray-500">
                      ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <button
                    onClick={() => removeFile(file.name)}
                    className="p-1 text-gray-400 hover:text-red-500"
                    title="Remove file"
                  >
                    <XIcon className="h-4 w-4" />
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mt-4 p-3 bg-red-50 text-red-600 rounded-md text-sm">
            {error}
          </div>
        )}

        {/* Upload Progress */}
        {isUploading && (
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Uploading...</span>
              <span>{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-indigo-600 h-2 rounded-full"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="mt-6">
          <button
            type="button"
            onClick={handleSubmit}
            disabled={
              isUploading ||
              (activeTab === 'upload' && selectedFiles.length === 0) ||
              (activeTab === 'link' && (!videoLink || !validateVideoLink(videoLink)))
            }
            className={`w-full py-2 px-4 rounded-md text-white font-medium
              ${isUploading ||
                (activeTab === 'upload' && selectedFiles.length === 0) ||
                (activeTab === 'link' && (!videoLink || !validateVideoLink(videoLink)))
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-indigo-600 hover:bg-indigo-700 transition-colors'
              }`}
          >
            {isUploading ? 'Processing...' : 'Upload and Transcribe'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FileUpload;