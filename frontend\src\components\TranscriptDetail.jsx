import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { ArrowLeftIcon, ClockIcon, DownloadIcon, ShareIcon, Trash2Icon } from 'lucide-react';

const TranscriptDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transcript, setTranscript] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [copied, setCopied] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchTranscript = async () => {
      try {
        setIsLoading(true);
        const response = await axios.get(`http://localhost:8000/transcripts/${id}`);
        setTranscript(response.data);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching transcript:', err);
        setError('Failed to load transcript. Please try again.');
        setIsLoading(false);
      }
    };

    fetchTranscript();
  }, [id]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const handleCopyToClipboard = () => {
    if (transcript) {
      navigator.clipboard.writeText(transcript.transcription)
        .then(() => {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(err => console.error('Failed to copy text:', err));
    }
  };

  const downloadTranscript = () => {
    if (transcript) {
      const element = document.createElement('a');
      const file = new Blob([transcript.transcription], {type: 'text/plain'});
      element.href = URL.createObjectURL(file);
      element.download = `${transcript.filename.split('.')[0]}_transcript.txt`;
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
    }
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await axios.delete(`http://localhost:8000/transcripts/${id}`);
      navigate('/transcripts', { replace: true });
    } catch (err) {
      console.error('Error deleting transcript:', err);
      setError('Failed to delete transcript. Please try again.');
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 rounded-lg mx-auto max-w-3xl mt-6">
        <h2 className="text-red-700 font-bold text-lg mb-2">Error</h2>
        <p>{error}</p>
        <Link 
          to="/transcripts" 
          className="inline-flex items-center mt-4 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Back to Transcripts
        </Link>
      </div>
    );
  }

  if (!transcript) {
    return (
      <div className="p-6 bg-yellow-50 rounded-lg mx-auto max-w-3xl mt-6">
        <h2 className="text-yellow-700 font-bold text-lg mb-2">Transcript Not Found</h2>
        <p>The transcript you're looking for could not be found.</p>
        <Link 
          to="/transcripts" 
          className="inline-flex items-center mt-4 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Back to Transcripts
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <Link 
          to="/transcripts" 
          className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-1" />
          Back to All Transcripts
        </Link>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="border-b border-gray-200 bg-gray-50 p-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">{transcript.filename}</h1>
              <div className="flex items-center text-sm text-gray-500">
                <ClockIcon className="w-4 h-4 mr-1" />
                <span>{formatDate(transcript.timestamp)}</span>
              </div>
            </div>
            
            <div>
              {showDeleteConfirm ? (
                <div className="flex items-center space-x-2">
                  <button 
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm"
                  >
                    {isDeleting ? 'Deleting...' : 'Confirm Delete'}
                  </button>
                  <button 
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm"
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <button 
                  onClick={() => setShowDeleteConfirm(true)}
                  className="p-2 text-gray-500 hover:text-red-500 transition-colors"
                >
                  <Trash2Icon className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <div className="flex justify-end space-x-2 mb-4">
            <button 
              onClick={handleCopyToClipboard}
              className={`
                inline-flex items-center px-3 py-2 border border-gray-300 rounded-md 
                ${copied ? 'bg-green-50 text-green-700 border-green-300' : 'bg-white text-gray-700 hover:bg-gray-50'}
                transition-colors
              `}
            >
              <ShareIcon className="w-4 h-4 mr-2" />
              {copied ? 'Copied!' : 'Copy Text'}
            </button>
            
            <button 
              onClick={downloadTranscript}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50"
            >
              <DownloadIcon className="w-4 h-4 mr-2" />
              Download
            </button>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-6 whitespace-pre-wrap font-mono text-sm">
            {transcript.transcription}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TranscriptDetail;