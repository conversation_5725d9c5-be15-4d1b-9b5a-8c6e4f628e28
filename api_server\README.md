# API Server Documentation

This is the backend API server for the voice-activated RAG application. It handles audio processing, transcription, and chat functionality using FastAPI.

## Components

### 1. Core Server Components
- `server.py`: Main FastAPI application that:
  - Handles audio file uploads and processing
  - Manages video transcription
  - Implements chat endpoints
  - Provides text-to-speech functionality
  - Manages FAISS vector storage integration

### 2. Audio Processing
- `whisper_s2t`: Directory containing Whisper speech-to-text implementation
- `whisperx_test.py`: Test implementation for WhisperX
- `audio_inputs`: Directory for storing input audio files
- `audio_output`: Directory for storing generated TTS audio

### 3. Vector Storage
- `faiss_backend.py`: Implementation of FAISS vector storage and retrieval
- `faiss_backend_requirements.txt`: Dependencies for FAISS backend

### 4. Model Resources
- `tts`: Text-to-speech model resources
- `audio_only`: Directory containing speaker audio samples

## Key Features

### 1. Speech Processing
- Real-time speech-to-text transcription using Whisper
- Support for both local audio files and YouTube videos
- Configurable speech processing parameters

### 2. Video Processing
- YouTube video URL processing
- Video transcription and chunking
- Metadata management for processed videos

### 3. Chat System
- RAG-based chat functionality
- Context-aware responses using retrieved video transcripts
- Text-to-speech output for responses

### 4. Data Management
- Transcript storage and retrieval
- FAISS-based vector search for efficient context retrieval
- In-memory metadata management

## API Endpoints

### 1. Core Endpoints
- `/transcribe`: Handle audio file uploads and transcription
- `/chat`: Process chat queries and generate responses
- `/tts`: Text-to-speech conversion
- `/health`: Health check endpoint

### 2. Video Processing
- `/process_link`: Handle YouTube video URLs
- `/transcripts`: Manage transcript metadata

## Dependencies

### Core Dependencies
- FastAPI
- Whisper
- FAISS
- PyTorch
- TTS
- Pytube

### Development Dependencies
- uvicorn
- httpx
- Pydantic

## Configuration

### Environment Variables
- `BACKEND_EMBED_URL`: URL for embedding service
- `SPEAKER_WAV`: Path to speaker audio sample
- `SPEAKER_ID`: ID for TTS speaker

### Model Configuration
- Device: CUDA/CPU
- Compute Type: float16/float32
- Batch Size: Configurable
- Max Speech Length: Configurable

## Usage

1. Run the server:
```bash
uvicorn server:app --host localhost --port 8000
```

2. Make API requests:
```python
import requests

# Transcribe audio
files = {'file': open('audio.wav', 'rb')}
response = requests.post('http://localhost:8000/transcribe', files=files)

# Process YouTube video
response = requests.post('http://localhost:8000/process_link', json={'url': 'https://youtube.com/...'})

# Chat with system
response = requests.post('http://localhost:8000/chat', json={'query': 'What was discussed in the video?'})

#### In one terminal
```bash
python -m venv server_env # transcription and tts

server_env\Scripts\activate.bat

pip install -r server_requirements.txt

python server.py # in server_env
```

#### In another terminal
```bash
python -m venv faiss_env # faiss and chat with ollama model

faiss_env\Scripts\activate.bat

pip install -r faiss_backend_requirements.txt
```

```bash
uvicorn new_faiss_backend:app --port 8001 --reload
```

#### In another terminal
```bash
ollama serve
```

#### Audio outputs
stored in `audio_output/`