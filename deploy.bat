@echo off
setlocal enabledelayedexpansion

echo 🚀 HPE Video Intelligence Platform Deployment
echo ==============================================

REM Check if .env file exists
if not exist .env (
    echo ❌ .env file not found!
    echo 📝 Please copy .env.example to .env and configure your settings:
    echo    copy .env.example .env
    echo    # Edit .env with your GROQ_API_KEY and DOCKER_USERNAME
    pause
    exit /b 1
)

REM Load environment variables from .env file
for /f "usebackq tokens=1,2 delims==" %%a in (.env) do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

REM Check required environment variables
if "%GROQ_API_KEY%"=="" (
    echo ❌ GROQ_API_KEY not configured!
    echo 📝 Please set your GROQ API key in .env file
    echo    Get your key from: https://console.groq.com/keys
    pause
    exit /b 1
)

if "%GROQ_API_KEY%"=="your_groq_api_key_here" (
    echo ❌ GROQ_API_KEY not configured!
    echo 📝 Please set your GROQ API key in .env file
    echo    Get your key from: https://console.groq.com/keys
    pause
    exit /b 1
)

if "%DOCKER_USERNAME%"=="" (
    echo ❌ DOCKER_USERNAME not configured!
    echo 📝 Please set your Docker Hub username in .env file
    pause
    exit /b 1
)

if "%DOCKER_USERNAME%"=="your-dockerhub-username" (
    echo ❌ DOCKER_USERNAME not configured!
    echo 📝 Please set your Docker Hub username in .env file
    pause
    exit /b 1
)

echo ✅ Environment variables configured
echo 🐳 Docker Username: %DOCKER_USERNAME%
echo 🔑 GROQ API Key: %GROQ_API_KEY:~0,10%...

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running!
    echo 📝 Please start Docker Desktop and try again
    pause
    exit /b 1
)

echo ✅ Docker is running

REM Pull latest images
echo 📥 Pulling latest images from Docker Hub...
docker-compose -f docker-compose.prod.yml pull

REM Stop existing containers
echo 🛑 Stopping existing containers...
docker-compose -f docker-compose.prod.yml down

REM Start services
echo 🚀 Starting services...
docker-compose -f docker-compose.prod.yml up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM Health checks
echo 🔍 Performing health checks...

REM Check server health
curl -f http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Server (port 8000) health check failed
) else (
    echo ✅ Server (port 8000) is healthy
)

REM Check FAISS backend health
curl -f http://localhost:8001/health >nul 2>&1
if errorlevel 1 (
    echo ❌ FAISS Backend (port 8001) health check failed
) else (
    echo ✅ FAISS Backend (port 8001) is healthy
)

REM Check frontend
curl -f http://localhost/ >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend (port 80) is not accessible
) else (
    echo ✅ Frontend (port 80) is accessible
)

echo.
echo 🎉 Deployment completed!
echo 📱 Access your application at:
echo    Frontend: http://localhost
echo    API Server: http://localhost:8000
echo    FAISS Backend: http://localhost:8001
echo.
echo 📊 To view logs: docker-compose -f docker-compose.prod.yml logs
echo 🛑 To stop: docker-compose -f docker-compose.prod.yml down
echo.
pause
