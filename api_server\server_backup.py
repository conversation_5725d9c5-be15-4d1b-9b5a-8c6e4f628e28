from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
import whisper_s2t
import torch
import time
import gc
import uvicorn
import os
import json
import requests
from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional
import pytubefix
import re

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

device = "cuda"
# device = "cpu"
compute_type = "float16"
# compute_type = "float32"
batch_size = 8
max_speech_len = 29 # default

model = whisper_s2t.load_model("small", backend="CTranslate2", device=device, compute_type=compute_type, max_speech_len = max_speech_len)

# Create a transcripts directory if it doesn't exist
transcripts_dir = "transcripts"
os.makedirs(transcripts_dir, exist_ok=True)

class TranscriptMeta(BaseModel):
    id: str
    filename: str
    timestamp: str
    duration: Optional[float] = None
    length: Optional[int] = None

class TranscriptWithContent(TranscriptMeta):
    transcription: str

class LinkRequest(BaseModel):
    url: str

# In-memory list to store transcript metadata
transcripts_index = []

# Load existing transcripts metadata on startup
def load_transcripts_index():
    index_path = os.path.join(transcripts_dir, "index.json")
    if os.path.exists(index_path):
        with open(index_path, "r") as f:
            return json.load(f)
    return []

# Save transcripts index
def save_transcripts_index():
    index_path = os.path.join(transcripts_dir, "index.json")
    with open(index_path, "w") as f:
        json.dump(transcripts_index, f, indent=4)


def clean_transcription_data(data):
    cleaned_data = []
    
    for item in data:
        cleaned_item = {
            key: value for key, value in item.items() 
            if key not in ['avg_logprob', 'no_speech_prob']
        }
        cleaned_data.append(cleaned_item)
    
    return cleaned_data

# Load existing transcripts on startup
transcripts_index = load_transcripts_index()

@app.post("/transcribe/")
async def transcribe_audio(file: UploadFile = File(...), speech_len: int = Form(29)):
    print(f"Received file: {file.filename}, speech_len: {speech_len}")

    global max_speech_len
    global model

    # Generate a unique ID using timestamp
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = f"{timestamp}_{file.filename.replace(' ', '_')}"
    safe_filename = file.filename.replace(' ', '_')
    
    # Create temporary file for processing
    temp_file = f"temp_{safe_filename}"
    with open(temp_file, "wb") as buffer:
        buffer.write(await file.read())
    
    start = time.time()
    
    files = [temp_file]
    lang_codes = ["en"]
    tasks = ["transcribe"]
    initial_prompts = [None]
    
    print(f"Reinitializing model with new max_speech_len: {speech_len}  {max_speech_len}")
    
    if(speech_len != max_speech_len):
        print(f"Reinitializing model with new max_speech_len: {speech_len}")
        del model
        gc.collect()

        if device == "cuda":
            torch.cuda.empty_cache()

        max_speech_len = speech_len
        model = whisper_s2t.load_model("small", backend="CTranslate2", device=device, compute_type=compute_type, max_speech_len = speech_len)
        print(f"Reinitialized model with new max_speech_len: {max_speech_len}")


    out = model.transcribe_with_vad(files, lang_codes=lang_codes, tasks=tasks, initial_prompts=initial_prompts, batch_size=batch_size)
    
    # Create transcript-specific directory
    transcript_dir = os.path.join(transcripts_dir, unique_id)
    os.makedirs(transcript_dir, exist_ok=True)
    
    # Save VTT format
    vtt_path = os.path.join(transcript_dir, "transcript.vtt")
    whisper_s2t.write_outputs(out, format='vtt', op_files=[vtt_path])
    
    # Save JSON format
    json_path = os.path.join(transcript_dir, "transcript.json")
    whisper_s2t.write_outputs(out, format='json', op_files=[json_path])
    
    # Clean and update JSON
    with open(json_path, "r") as f:
        data = json.load(f)
    
    cleaned_data = clean_transcription_data(data)
    with open(json_path, "w") as f:
        json.dump(cleaned_data, f, indent=4)
    
    # Read plain text transcript
    with open(vtt_path, "r") as f:
        vtt_content = f.read()
    transcript_text = vtt_content[6:] if vtt_content.startswith("WEBVTT") else vtt_content
    
    # Save plain text format
    txt_path = os.path.join(transcript_dir, "transcript.txt")
    with open(txt_path, "w") as f:
        f.write(transcript_text)
    
    # Clean up temporary file
    os.remove(temp_file)
    
    end = time.time()
    processing_time = end - start
    print(f"\nTime taken for transcription: {processing_time}")
    
    # Create metadata entry
    transcript_meta = {
        "id": unique_id,
        "filename": file.filename,
        "timestamp": datetime.now().isoformat(),
        "duration": processing_time,
        "length": len(transcript_text)
    }
    
    # Add to index and save
    transcripts_index.append(transcript_meta)
    save_transcripts_index()
    
    gc.collect()
    torch.cuda.empty_cache()
    
    return {
        "id": unique_id,
        "transcription": transcript_text,
        "filename": file.filename,
        "timestamp": transcript_meta["timestamp"]
    }

@app.delete("/transcripts/{transcript_id}")
async def delete_transcript(transcript_id: str, speech_len: int = 29):
    # Find transcript in index
    transcript_index = next((i for i, t in enumerate(transcripts_index) if t["id"] == transcript_id), None)
    
    if transcript_index is None:
        raise HTTPException(status_code=404, detail="Transcript not found")
    
    # Remove from index
    removed_transcript = transcripts_index.pop(transcript_index)
    save_transcripts_index()
    
    # Remove transcript files
    transcript_dir = os.path.join(transcripts_dir, transcript_id)
    if os.path.exists(transcript_dir):
        import shutil
        shutil.rmtree(transcript_dir)
    
    return {"message": "Transcript deleted successfully", "id": transcript_id}

@app.post("/link_transcript/")
async def process_link(link_request: LinkRequest):
    url = link_request.url
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

    try:
        yt = pytubefix.YouTube(url)

        if 'a.en' not in yt.captions:
            raise HTTPException(status_code=400, detail="No English captions available for this YouTube video")

        srt_captions = yt.captions['a.en'].generate_srt_captions()
        unique_id = f"{timestamp}_youtube"

        transcript_dir = os.path.join("transcripts", unique_id)
        os.makedirs(transcript_dir, exist_ok=True)

        srt_path = os.path.join(transcript_dir, "transcript.srt")
        with open(srt_path, "w") as f:
            f.write(srt_captions)

        return {
            "id": unique_id,
            "transcription": srt_captions,
            "filename": f"YouTube: {yt.title}",
            "url": url,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing link: {str(e)}")

@app.get("/transcripts/")
async def get_all_transcripts():
    return transcripts_index

@app.get("/transcripts/{transcript_id}")
async def get_transcript(transcript_id: str):
    # Find transcript in index
    transcript_meta = next((t for t in transcripts_index if t["id"] == transcript_id), None)
    
    if not transcript_meta:
        return {"error": "Transcript not found"}
    
    # Read the transcript content
    txt_path = os.path.join(transcripts_dir, transcript_id, "transcript.txt")
    
    if not os.path.exists(txt_path):
        return {"error": "Transcript file not found"}
    
    with open(txt_path, "r") as f:
        transcript_text = f.read()
    
    return {
        **transcript_meta,
        "transcription": transcript_text
    }

if __name__ == "__main__":
    uvicorn.run(app, host="localhost", port=8000)