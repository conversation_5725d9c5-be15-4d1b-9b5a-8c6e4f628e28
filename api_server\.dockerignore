# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
server_env/
faiss_env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Large model files (will be downloaded during build)
*.bin
*.pt
*.pth

# Output directories (will be created as volumes)
transcripts/
videos/
audio_output/
embeddings/
merged_embeddings/

# Ignore files
ignore/

# Documentation
README.md
*.md

# Other
.env
.env.local
.env.*.local
