{"docstore/ref_doc_info": {"173f716b-af68-48f9-89d4-d5f15beb9b60": {"node_ids": ["f0de9d96-6635-42bc-8170-3bcec920365b"], "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}}, "fcf72281-9f80-40f4-80ce-98225378d6cf": {"node_ids": ["6eac2685-ee4f-4fef-9d9a-150f50affaa0"], "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}}, "b2243f3b-762f-4332-a87a-3665833e5071": {"node_ids": ["897fab3c-65f9-49a8-a82a-b58efd74d844"], "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}}, "81512a0a-9787-4d40-ad6d-18d35944d935": {"node_ids": ["a8e87d2b-9867-4383-90f6-87c809c26374"], "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}}, "93d76cb3-f1fb-4d31-a52f-e525cf96b8fd": {"node_ids": ["f90b7267-20b2-462f-a551-b83f3fe77d06"], "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}}, "df81dd36-d6cc-4649-bfe9-b5a538e15aa9": {"node_ids": ["8adefc4b-4374-42fc-a2fa-c4db772c462a"], "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}}, "14041998-c18f-45bc-9977-206bc348e963": {"node_ids": ["d9001df7-4bdc-4eca-b9cf-f75fe70b9cf5"], "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}}, "ab3b18df-a59f-44db-9fce-a361da9babd6": {"node_ids": ["68eaaf37-d65a-4672-bd4b-990a934125de"], "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}}, "5bc6d85a-049a-4434-8b09-fd457b9ae5bd": {"node_ids": ["13b34f12-ae56-485b-8ec7-2f6b2d61c68a"], "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}}, "aaae7942-97bd-4734-8f4e-944963657b0e": {"node_ids": ["d8c687fd-c2b9-4f34-9b73-2cca408d810f"], "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}}, "1a2d96f8-7c60-4da4-a496-69908e0267c1": {"node_ids": ["961f7c06-eb20-410b-8707-570369df8a50"], "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}}, "4bf3fb8e-6262-4f75-a0f4-b6796a36797a": {"node_ids": ["c84363a2-a21a-42f1-97c3-99842e44a8ea"], "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}}, "eca7c58f-59ab-4c92-8d5f-7b8532d0c4b8": {"node_ids": ["daa75629-0d4b-48a7-a1d5-9ccb50621a6a"], "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}}, "56c26ab2-fa9d-4a16-b027-79f26ec13959": {"node_ids": ["74cc2355-8293-42ca-a3f3-efa2dfc824db"], "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}}, "25bdc5e9-9129-4af3-aaa6-8f4a777a7916": {"node_ids": ["696c3a2c-5de4-4e71-8d47-6ad6031cd32e"], "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}}, "81d5536f-f5f1-4a8a-a608-2408e5436d53": {"node_ids": ["974bfc19-ef0a-4c4d-acce-aaa438c54813"], "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}}, "f6ea6583-5e20-4063-a491-1be5c4acd1ec": {"node_ids": ["0c578996-ff2a-461e-9271-15aace54ad63"], "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}}, "42a03687-699e-4447-aed4-ef50253c32de": {"node_ids": ["a55b29a3-7099-4c46-9710-cb70dbe5107e"], "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}}, "e1b4a5a2-a37b-4aa7-80fa-bc0a1a058707": {"node_ids": ["719bc6d6-c62e-4bca-b191-bb64fcc1f366"], "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}}, "cf03b2e5-6aba-4d37-9d0a-c968e0fdf048": {"node_ids": ["1d87cd85-7b1e-4323-a2cb-baf69f0097f6"], "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}}, "5208887a-0aa6-44b5-a5ce-1c139e1f59b6": {"node_ids": ["143d4837-b719-4565-8c79-542177e89c86"], "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}}, "3d5bc187-db46-4ca1-9070-d133c8ac652e": {"node_ids": ["0cc913f7-381d-4d76-b1f3-36c2e5463196"], "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}}, "cc4d06c9-2dc0-44d2-80e4-4b022e2986cb": {"node_ids": ["46641d6f-2918-48ba-8dfb-a5442603dd81"], "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}}, "6c416079-407e-4fa4-900f-54a86c740742": {"node_ids": ["55d0ebae-ba31-4cb2-9178-eef1ae71467c"], "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}}, "1c91213c-db08-41b7-8ae0-f2339c97c99a": {"node_ids": ["864354d6-04ad-4f52-a4d2-735c27bddf3e"], "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}}, "9daa5958-aac1-48c6-bfb2-b8942123b14d": {"node_ids": ["81115b5b-6637-4e39-a835-27f6b1f46ce9"], "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}}, "8600a13c-939a-4a6b-a105-90add69b5dbb": {"node_ids": ["79eceba8-3a70-4b23-badd-dc4e3ea6166c"], "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}}, "aee6e897-92e3-44fa-93c3-44eed568f9b7": {"node_ids": ["db6ebbaa-0c3f-4856-a72c-f867f63bf343"], "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}}, "75ca3661-3df4-4ca4-a540-d26f5b4d7098": {"node_ids": ["f904c5ba-5b44-4384-a5cc-aa8ed0740c79"], "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}}, "006505af-f1c0-4d87-81e9-5dcdd89173ce": {"node_ids": ["f0ec30ba-37f5-4e64-8856-1637703a31ef"], "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}}, "db95727f-89e6-4d3a-8205-3ecf4e152900": {"node_ids": ["aeb298ae-0fbb-4c0a-bd24-e4e271b98b6b"], "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}}, "5f739082-1605-416c-85c8-e4394574fb33": {"node_ids": ["c92091f0-f7d6-4592-84c0-d2d3f8f66e8e"], "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}}, "a5ae1367-ed5c-420a-b7bb-3ebcc80cf13d": {"node_ids": ["49d77cf4-8e45-4351-baea-87c204e60fd2"], "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}}, "74e4baed-24b0-4770-898e-2cb2471e1dc1": {"node_ids": ["0e90d3a0-30ec-472d-a88d-50918e642470"], "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}}, "8e7819d6-14c5-4259-ab94-2ae78e4f6534": {"node_ids": ["7683b1e1-bd5c-4c24-8440-f32a1559249c"], "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}}, "92108841-daef-45b2-81f9-b90b043534f8": {"node_ids": ["ba7dd449-903c-46da-9a32-b34ac4fe4aa7"], "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}}, "89cad037-3cd0-40c1-a13d-48954b152a50": {"node_ids": ["d6291acd-5a28-412a-a4a5-56fe9320f969"], "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}}, "84eb41fb-4b99-42fb-8263-6b6773e745e7": {"node_ids": ["18ce4f7a-df99-453d-99da-11eb8c7871fb"], "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}}, "d944846f-285b-4641-8524-1486d47b3bb7": {"node_ids": ["271c89e4-8026-4f78-86c1-be203cc909d7"], "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}}, "34a2394e-a459-45d5-ba17-748ca422dd7e": {"node_ids": ["e857c5e1-c8cf-490c-b178-c311a49d764b"], "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}}, "b1e61df7-4462-4b84-a4ed-8c78ef71a548": {"node_ids": ["de137a35-b74f-46d1-b740-1bf206c45e6b"], "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}}, "734ac39c-3d14-4486-a55b-0f3b3ca8197b": {"node_ids": ["e6ef2133-5297-4064-b535-9ad1a56eeae9"], "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}}, "4e7531b9-84ba-4055-8863-8f9a285a4613": {"node_ids": ["c77759df-7595-436f-bb49-bf279e04bf1e"], "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}}, "6d053aa8-8e93-4ec6-a42d-ec95a03408cc": {"node_ids": ["47b0f35a-fb57-4275-8546-a68ded99e7f5"], "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}}, "c6cc0bff-ef7d-4a73-86c2-befb834c0fe5": {"node_ids": ["a519b0aa-d56a-4efc-9515-674dbb7e5295"], "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}}, "f13c080a-aadc-46fe-94b9-c91022f2fecb": {"node_ids": ["d1871840-13e0-4a46-bc21-c6ed5da00d19"], "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}}}, "docstore/data": {"f0de9d96-6635-42bc-8170-3bcec920365b": {"__data__": {"id_": "f0de9d96-6635-42bc-8170-3bcec920365b", "embedding": null, "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "173f716b-af68-48f9-89d4-d5f15beb9b60", "node_type": "4", "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "a9fa4d0d1bce2f1dc8a516b5936fb9ab0d83d7cbc7a90ac6f508d494fa68f0fc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Hello friends, I am going to start a course on algorithms. Algorithm as a subject. Algorithm is a common subject for computer science engineering students. Most of the universities offer this course as a part of syllabus and this is a very core subject and very important subject. And students face some difficulties in some of the topics in this one. They could not understand them very clearly.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 396, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6eac2685-ee4f-4fef-9d9a-150f50affaa0": {"__data__": {"id_": "6eac2685-ee4f-4fef-9d9a-150f50affaa0", "embedding": null, "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fcf72281-9f80-40f4-80ce-98225378d6cf", "node_type": "4", "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "336390f869673fc93caf19d31ae008ef60097c19023e885e7c7405ccbf9b94ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So here I am going to make the subject more and more simple so that you can easily understand and even you can practice that and also remember it easily. The importance of the subject is that apart from theoretical examination, it is also important for competitive exams. And even programming contest, most of them are designed from this subject only. If any programming challenge is there, then mostly they pick up the questions from this subject.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 448, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "897fab3c-65f9-49a8-a82a-b58efd74d844": {"__data__": {"id_": "897fab3c-65f9-49a8-a82a-b58efd74d844", "embedding": null, "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b2243f3b-762f-4332-a87a-3665833e5071", "node_type": "4", "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "hash": "7e57206fe95b3731b94c0a71265ce769132ce4c9e113eb0c7e5bcfada26a47d6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So this is one of the challenging subject for the student because they get a chance to solve different type of problems and they get the strategy or approach for solving the problem. So some students fail to understand it properly so they could not get the approach or the strategy for solving the problems and they feel that they are lacking in the logic development or strategic development of a program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 406, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a8e87d2b-9867-4383-90f6-87c809c26374": {"__data__": {"id_": "a8e87d2b-9867-4383-90f6-87c809c26374", "embedding": null, "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "81512a0a-9787-4d40-ad6d-18d35944d935", "node_type": "4", "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "hash": "0a053eb9db12d7c321c9e7e26d05d4171504e708cfe6ac7c93b06793295b2825", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So I will be covering in depth each and everything right from the basics to the depth. So that you will be able to answer any type of question from this one. I will cover the topics such that just you look at the question you can get the answer that I guarantee you. So the thing is I will cover those points which are useful for solving the questions.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 352, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f90b7267-20b2-462f-a551-b83f3fe77d06": {"__data__": {"id_": "f90b7267-20b2-462f-a551-b83f3fe77d06", "embedding": null, "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "93d76cb3-f1fb-4d31-a52f-e525cf96b8fd", "node_type": "4", "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "hash": "2fe908711069d923a9ac52b9b2e378427315b24f1c874b0aa2fe2967dee6ff93", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Some job interviews or even for any other exam or some entrance exams like gate exam, the cushions are framed from algorithms also. There is also one of the key subject there. So you will be able to answer any type of question from there. Now towards the lectures in the order, I'll be giving the numbers for each topic so that you follow that order, you follow the sequence because I may be breaking the major topic into small topics.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 435, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8adefc4b-4374-42fc-a2fa-c4db772c462a": {"__data__": {"id_": "8adefc4b-4374-42fc-a2fa-c4db772c462a", "embedding": null, "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "df81dd36-d6cc-4649-bfe9-b5a538e15aa9", "node_type": "4", "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "480fcfc2a237e6ba77e2fff1ebabf05d4fb5ce330272919b624cf83dc5e25074", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And one more important thing is your feedback is very important for me because already I have made few prepared few videos using presentations and other tools but now I am giving a lecture on whiteboard so maybe I am new for the video quality and audio quality all these things so technically once I get set up so I need your feedback so I can make my videos more and more better", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 379, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d9001df7-4bdc-4eca-b9cf-f75fe70b9cf5": {"__data__": {"id_": "d9001df7-4bdc-4eca-b9cf-f75fe70b9cf5", "embedding": null, "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "14041998-c18f-45bc-9977-206bc348e963", "node_type": "4", "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "hash": "6a2e25be111a47bb6bae3c5129e80849cda2092ffc07f5951c07f6a8da82eb8f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So your feedback is important for me to know how the video quality and audio quality you are getting it on your devices because I have checked on different devices and I found that the results are different. So what devices you are using, what I should improve, voice or what so I need your feedback. Let us start with just introduction. What is an Elcortone?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 359, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "68eaaf37-d65a-4672-bd4b-990a934125de": {"__data__": {"id_": "68eaaf37-d65a-4672-bd4b-990a934125de", "embedding": null, "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ab3b18df-a59f-44db-9fce-a361da9babd6", "node_type": "4", "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "hash": "830aab74de03c0828a98481cbb72deb5cbc23d2df6bcb07c7f07f9379c7c1e07", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "What is an Elgortum? Why it is written? When it is written? Who will write it? Let us get the answers for these. See, algorithm as a definition, it's a common definition everybody knows that, it's a step by step procedure for solving a computational problem. Yes, it is a step by step procedure for solving a computational problem. Then what is a program? Program is also a step by step procedure for solving a problem. Then what is the difference between program and Elgortum?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 477, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "13b34f12-ae56-485b-8ec7-2f6b2d61c68a": {"__data__": {"id_": "13b34f12-ae56-485b-8ec7-2f6b2d61c68a", "embedding": null, "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5bc6d85a-049a-4434-8b09-fd457b9ae5bd", "node_type": "4", "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "hash": "c65c47d91c7d86a85ad8e8fc97834ebcb84551c0685952ccf84059faf7ec709f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So by comparing program and algorithm I can make you understand the importance of algorithm, meaning of algorithm. Let us take it. Here is an algorithm and here is a program. Let us see the first thing, first difference. If you know the software development life cycle, means the phases of developing a software project. In the phases of development of software project there are two important phases.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 401, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d8c687fd-c2b9-4f34-9b73-2cca408d810f": {"__data__": {"id_": "d8c687fd-c2b9-4f34-9b73-2cca408d810f", "embedding": null, "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aaae7942-97bd-4734-8f4e-944963657b0e", "node_type": "4", "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "5fb38245dc6405b7af36ebda47abf4bafc417742a1334cb7c886cc1974ee35c4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "design phase and the implementation phase. Now if you are not aware of it let me tell you whatever you want to manufacture or construct something by engineering procedures then first step one of the important step is designing. First you do designing make your design perfect and thorough so that you can understand what you are going to construct what you are going to develop and when you are sure what you are going to develop then you start to develop.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 456, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "961f7c06-eb20-410b-8707-570369df8a50": {"__data__": {"id_": "961f7c06-eb20-410b-8707-570369df8a50", "embedding": null, "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1a2d96f8-7c60-4da4-a496-69908e0267c1", "node_type": "4", "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "bbf925bd7428bba5ebbeb0463f2aa04e17c3d80945545021e1a77bf817ce6a1d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can't develop anything, you cannot construct anything on trial and error basis. Like you constructed something, again no one is wrong, destroyed and again created a new one. So no. But it is easy in software engineering. Software engineer can write some program and change the mind, delete the program and again start writing them. So that's why we cannot get the feel that we have wasted so much amount of time in writing so useless program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c84363a2-a21a-42f1-97c3-99842e44a8ea": {"__data__": {"id_": "c84363a2-a21a-42f1-97c3-99842e44a8ea", "embedding": null, "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4bf3fb8e-6262-4f75-a0f4-b6796a36797a", "node_type": "4", "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "hash": "c3f3ae6ac56de307c373f644d10a2dfdfb863aa1541a07e3724d8d0baba22cdf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So the point is first you design and then you write the program. So at the design time what do you use? So you are not writing a program then what do you write? So that same program we write it in simple English like statements that are easy to understand without using proper syntax and we may not be writing on machine that is on computer, we may be writing on paper.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 369, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "daa75629-0d4b-48a7-a1d5-9ccb50621a6a": {"__data__": {"id_": "daa75629-0d4b-48a7-a1d5-9ccb50621a6a", "embedding": null, "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "eca7c58f-59ab-4c92-8d5f-7b8532d0c4b8", "node_type": "4", "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "hash": "b6726b0de7a7cb65c8a31aebeea36c34e820569bab75518dc7f9ee6b58a4264e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "or even if you are using a machine also then you will not be writing in some language, you will be writing in MS Word or notepad like application. So just you are getting familiar how your program is going to work. So that is nothing but an algorithm. So algorithms are written at design time and when the programs are written they are written at implementation time, design time and implementation.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 399, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "74cc2355-8293-42ca-a3f3-efa2dfc824db": {"__data__": {"id_": "74cc2355-8293-42ca-a3f3-efa2dfc824db", "embedding": null, "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "56c26ab2-fa9d-4a16-b027-79f26ec13959", "node_type": "4", "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "112cf2c41b1e85acb4c3f9efa7214f83ad9c4cad253f587857bbbe21b74217c1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So first you make a design what your procedure is going to do, what your software is going to do, come up with some design and that design you convert into a program. Then what do you call the person who do designing? Whether the programmer does that? Yes if programmer is capable of doing that he can do it otherwise the person who will do this one should have the domain knowledge, the problems knowledge, knowledge about the problem and its solution.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 453, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "696c3a2c-5de4-4e71-8d47-6ad6031cd32e": {"__data__": {"id_": "696c3a2c-5de4-4e71-8d47-6ad6031cd32e", "embedding": null, "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "25bdc5e9-9129-4af3-aaa6-8f4a777a7916", "node_type": "4", "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "04bef88b5f0dcaff0f1f1e55bb08395099257accad5806c999de817df90386d1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He can give a solution, the one who understands the problem and has the domain knowledge. Suppose you are developing a software or an algorithm or a program for account then accountant can understand accounting better. Or if you are writing an application or developing an application for hospital then doctors or the administrative staff of a hospital can understand the system better than a programmer.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 404, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "974bfc19-ef0a-4c4d-acce-aaa438c54813": {"__data__": {"id_": "974bfc19-ef0a-4c4d-acce-aaa438c54813", "embedding": null, "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "81d5536f-f5f1-4a8a-a608-2408e5436d53", "node_type": "4", "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "hash": "058e4c8e74d62b468c090a559f11923b8291d2249dee5646df7668d434b49533", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So mostly the one who is having a knowledge about the domain for which the application is being developed, they can write on the algorithm. So the person who writes should have domain knowledge. Who will write the programmer? Programmer? So programmers can also have domain knowledge. They can also have domain knowledge that he is acting as a designer and here he is acting as a programmer. Next, what is the language used for writing algorithm? You can use any language.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 472, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0c578996-ff2a-461e-9271-15aace54ad63": {"__data__": {"id_": "0c578996-ff2a-461e-9271-15aace54ad63", "embedding": null, "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f6ea6583-5e20-4063-a491-1be5c4acd1ec", "node_type": "4", "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "hash": "904eb1ca5e8cbc9017747b0ffa8acc872e73a567681c9265506ba9d1e7ecd56a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Any language, any language means English like language or you can use some mathematical notations. If you're writing English like language it will be like writing paras or samari but don't try it. If you want you can use mathematical notations also. It's better if you use mathematical notations. So any language can be used, any language means English language or some mathematical notations can be used as long as it is understandable by those people who are using it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 470, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a55b29a3-7099-4c46-9710-cb70dbe5107e": {"__data__": {"id_": "a55b29a3-7099-4c46-9710-cb70dbe5107e", "embedding": null, "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "42a03687-699e-4447-aed4-ef50253c32de", "node_type": "4", "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "hash": "6cb306b48dd708e5deb5b3abaafa45f1c4982f208e7cf7576ca213864b34f035", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Like if a designer has written an algorithm then the programmer will be writing a program for it. So designers should understand and also programmers should understand that one. So the team of programmers who are working on that project should be able to understand it. Then this is written only using programming language like C++, Java, Python. So you can use different languages for developing a program. The next one more important thing here", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "719bc6d6-c62e-4bca-b191-bb64fcc1f366": {"__data__": {"id_": "719bc6d6-c62e-4bca-b191-bb64fcc1f366", "embedding": null, "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e1b4a5a2-a37b-4aa7-80fa-bc0a1a058707", "node_type": "4", "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "hash": "75652f5626d3a034b092bfb06b8b469a8cc8e8192c64daa9dbfa68c5bc1fd114", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "When you write an L-Core, it's going to be hardware and software means operating system independent. It's not dependent on hardware, what machine you are going to use, what's the configuration of the machine and what's the operating system, either Linux operating system or Windows operating system, we don't bother about it. But when you write a program, it is dependent on hardware and operating system.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 405, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1d87cd85-7b1e-4323-a2cb-baf69f0097f6": {"__data__": {"id_": "1d87cd85-7b1e-4323-a2cb-baf69f0097f6", "embedding": null, "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cf03b2e5-6aba-4d37-9d0a-c968e0fdf048", "node_type": "4", "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "427c86b713e89ea116f9463b2a026e9bb592501861bb45c70c297ce3d7dc53dc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So when you develop the program you have to select some hardware on which you are going to run and also you have to select some operating system. You may be doing it for Linux or you may be doing it for Windows. So the method will be different and the environments are different. So this is important.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 301, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "143d4837-b719-4565-8c79-542177e89c86": {"__data__": {"id_": "143d4837-b719-4565-8c79-542177e89c86", "embedding": null, "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5208887a-0aa6-44b5-a5ce-1c139e1f59b6", "node_type": "4", "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "hash": "398adcfe82df433050774ab48668f1483369eeb41352f55468c2daf162e8eefb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Then next, after writing an algorithm we analyze, analyze an algorithm. Means we will study the algorithm to find out are we achieving the results perfectly or not and our algorithm is efficient or not in terms of time and space. We will see what does it mean by analysis. So we will analyze an algorithm. What do we do with the program? So you don't have to study the program. All the program is there. Just run it and check it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 429, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0cc913f7-381d-4d76-b1f3-36c2e5463196": {"__data__": {"id_": "0cc913f7-381d-4d76-b1f3-36c2e5463196", "embedding": null, "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "3d5bc187-db46-4ca1-9070-d133c8ac652e", "node_type": "4", "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "hash": "e22c92de10c79ced1f48786f7245fcf31b574c60484e8fe401ef8b54c4ab6634", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So we do testing, testing of the program. That's it. These are few differences between algorithm and program. These differences will help you understand what are algorithms. All right? Now one more thing I will tell you. The syntax of the language that we use, any language can be used but who actually write algorithms and who use them and who write the programs of programmers who are coming from university", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 409, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "46641d6f-2918-48ba-8dfb-a5442603dd81": {"__data__": {"id_": "46641d6f-2918-48ba-8dfb-a5442603dd81", "embedding": null, "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cc4d06c9-2dc0-44d2-80e4-4b022e2986cb", "node_type": "4", "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "hash": "611977a7f35968dc31b638dc4ad4a8e586ab079f13855a5d4cbb0da874f60956", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "mostly university graduate knows at least C language. So nowadays mostly in the name of algorithm we write C language program only. So the benefit is that the advantage that everybody knows C language now. So instead of writing some other language to confuse we can use C language only so that everybody can understand. So even at a school level C language is being taught so everybody is familiar with C language. So I may be using C language syntax for writing algorithm.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 473, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "55d0ebae-ba31-4cb2-9178-eef1ae71467c": {"__data__": {"id_": "55d0ebae-ba31-4cb2-9178-eef1ae71467c", "embedding": null, "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6c416079-407e-4fa4-900f-54a86c740742", "node_type": "4", "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "a9fa4d0d1bce2f1dc8a516b5936fb9ab0d83d7cbc7a90ac6f508d494fa68f0fc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Hello friends, I am going to start a course on algorithms. Algorithm as a subject. Algorithm is a common subject for computer science engineering students. Most of the universities offer this course as a part of syllabus and this is a very core subject and very important subject. And students face some difficulties in some of the topics in this one. They could not understand them very clearly.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 396, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "864354d6-04ad-4f52-a4d2-735c27bddf3e": {"__data__": {"id_": "864354d6-04ad-4f52-a4d2-735c27bddf3e", "embedding": null, "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1c91213c-db08-41b7-8ae0-f2339c97c99a", "node_type": "4", "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "336390f869673fc93caf19d31ae008ef60097c19023e885e7c7405ccbf9b94ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So here I am going to make the subject more and more simple so that you can easily understand and even you can practice that and also remember it easily. The importance of the subject is that apart from theoretical examination, it is also important for competitive exams. And even programming contest, most of them are designed from this subject only. If any programming challenge is there, then mostly they pick up the questions from this subject.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 448, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "81115b5b-6637-4e39-a835-27f6b1f46ce9": {"__data__": {"id_": "81115b5b-6637-4e39-a835-27f6b1f46ce9", "embedding": null, "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9daa5958-aac1-48c6-bfb2-b8942123b14d", "node_type": "4", "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "hash": "7e57206fe95b3731b94c0a71265ce769132ce4c9e113eb0c7e5bcfada26a47d6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So this is one of the challenging subject for the student because they get a chance to solve different type of problems and they get the strategy or approach for solving the problem. So some students fail to understand it properly so they could not get the approach or the strategy for solving the problems and they feel that they are lacking in the logic development or strategic development of a program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 406, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "79eceba8-3a70-4b23-badd-dc4e3ea6166c": {"__data__": {"id_": "79eceba8-3a70-4b23-badd-dc4e3ea6166c", "embedding": null, "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8600a13c-939a-4a6b-a105-90add69b5dbb", "node_type": "4", "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "hash": "0a053eb9db12d7c321c9e7e26d05d4171504e708cfe6ac7c93b06793295b2825", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So I will be covering in depth each and everything right from the basics to the depth. So that you will be able to answer any type of question from this one. I will cover the topics such that just you look at the question you can get the answer that I guarantee you. So the thing is I will cover those points which are useful for solving the questions.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 352, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "db6ebbaa-0c3f-4856-a72c-f867f63bf343": {"__data__": {"id_": "db6ebbaa-0c3f-4856-a72c-f867f63bf343", "embedding": null, "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aee6e897-92e3-44fa-93c3-44eed568f9b7", "node_type": "4", "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "hash": "2fe908711069d923a9ac52b9b2e378427315b24f1c874b0aa2fe2967dee6ff93", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Some job interviews or even for any other exam or some entrance exams like gate exam, the cushions are framed from algorithms also. There is also one of the key subject there. So you will be able to answer any type of question from there. Now towards the lectures in the order, I'll be giving the numbers for each topic so that you follow that order, you follow the sequence because I may be breaking the major topic into small topics.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 435, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f904c5ba-5b44-4384-a5cc-aa8ed0740c79": {"__data__": {"id_": "f904c5ba-5b44-4384-a5cc-aa8ed0740c79", "embedding": null, "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "75ca3661-3df4-4ca4-a540-d26f5b4d7098", "node_type": "4", "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "480fcfc2a237e6ba77e2fff1ebabf05d4fb5ce330272919b624cf83dc5e25074", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And one more important thing is your feedback is very important for me because already I have made few prepared few videos using presentations and other tools but now I am giving a lecture on whiteboard so maybe I am new for the video quality and audio quality all these things so technically once I get set up so I need your feedback so I can make my videos more and more better", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 379, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f0ec30ba-37f5-4e64-8856-1637703a31ef": {"__data__": {"id_": "f0ec30ba-37f5-4e64-8856-1637703a31ef", "embedding": null, "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "006505af-f1c0-4d87-81e9-5dcdd89173ce", "node_type": "4", "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "hash": "6a2e25be111a47bb6bae3c5129e80849cda2092ffc07f5951c07f6a8da82eb8f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So your feedback is important for me to know how the video quality and audio quality you are getting it on your devices because I have checked on different devices and I found that the results are different. So what devices you are using, what I should improve, voice or what so I need your feedback. Let us start with just introduction. What is an Elcortone?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 359, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "aeb298ae-0fbb-4c0a-bd24-e4e271b98b6b": {"__data__": {"id_": "aeb298ae-0fbb-4c0a-bd24-e4e271b98b6b", "embedding": null, "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "db95727f-89e6-4d3a-8205-3ecf4e152900", "node_type": "4", "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "hash": "830aab74de03c0828a98481cbb72deb5cbc23d2df6bcb07c7f07f9379c7c1e07", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "What is an Elgortum? Why it is written? When it is written? Who will write it? Let us get the answers for these. See, algorithm as a definition, it's a common definition everybody knows that, it's a step by step procedure for solving a computational problem. Yes, it is a step by step procedure for solving a computational problem. Then what is a program? Program is also a step by step procedure for solving a problem. Then what is the difference between program and Elgortum?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 477, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c92091f0-f7d6-4592-84c0-d2d3f8f66e8e": {"__data__": {"id_": "c92091f0-f7d6-4592-84c0-d2d3f8f66e8e", "embedding": null, "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5f739082-1605-416c-85c8-e4394574fb33", "node_type": "4", "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "hash": "c65c47d91c7d86a85ad8e8fc97834ebcb84551c0685952ccf84059faf7ec709f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So by comparing program and algorithm I can make you understand the importance of algorithm, meaning of algorithm. Let us take it. Here is an algorithm and here is a program. Let us see the first thing, first difference. If you know the software development life cycle, means the phases of developing a software project. In the phases of development of software project there are two important phases.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 401, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "49d77cf4-8e45-4351-baea-87c204e60fd2": {"__data__": {"id_": "49d77cf4-8e45-4351-baea-87c204e60fd2", "embedding": null, "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a5ae1367-ed5c-420a-b7bb-3ebcc80cf13d", "node_type": "4", "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "5fb38245dc6405b7af36ebda47abf4bafc417742a1334cb7c886cc1974ee35c4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "design phase and the implementation phase. Now if you are not aware of it let me tell you whatever you want to manufacture or construct something by engineering procedures then first step one of the important step is designing. First you do designing make your design perfect and thorough so that you can understand what you are going to construct what you are going to develop and when you are sure what you are going to develop then you start to develop.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 456, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0e90d3a0-30ec-472d-a88d-50918e642470": {"__data__": {"id_": "0e90d3a0-30ec-472d-a88d-50918e642470", "embedding": null, "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "74e4baed-24b0-4770-898e-2cb2471e1dc1", "node_type": "4", "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "bbf925bd7428bba5ebbeb0463f2aa04e17c3d80945545021e1a77bf817ce6a1d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can't develop anything, you cannot construct anything on trial and error basis. Like you constructed something, again no one is wrong, destroyed and again created a new one. So no. But it is easy in software engineering. Software engineer can write some program and change the mind, delete the program and again start writing them. So that's why we cannot get the feel that we have wasted so much amount of time in writing so useless program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7683b1e1-bd5c-4c24-8440-f32a1559249c": {"__data__": {"id_": "7683b1e1-bd5c-4c24-8440-f32a1559249c", "embedding": null, "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8e7819d6-14c5-4259-ab94-2ae78e4f6534", "node_type": "4", "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "hash": "c3f3ae6ac56de307c373f644d10a2dfdfb863aa1541a07e3724d8d0baba22cdf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So the point is first you design and then you write the program. So at the design time what do you use? So you are not writing a program then what do you write? So that same program we write it in simple English like statements that are easy to understand without using proper syntax and we may not be writing on machine that is on computer, we may be writing on paper.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 369, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ba7dd449-903c-46da-9a32-b34ac4fe4aa7": {"__data__": {"id_": "ba7dd449-903c-46da-9a32-b34ac4fe4aa7", "embedding": null, "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "92108841-daef-45b2-81f9-b90b043534f8", "node_type": "4", "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "hash": "b6726b0de7a7cb65c8a31aebeea36c34e820569bab75518dc7f9ee6b58a4264e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "or even if you are using a machine also then you will not be writing in some language, you will be writing in MS Word or notepad like application. So just you are getting familiar how your program is going to work. So that is nothing but an algorithm. So algorithms are written at design time and when the programs are written they are written at implementation time, design time and implementation.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 399, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d6291acd-5a28-412a-a4a5-56fe9320f969": {"__data__": {"id_": "d6291acd-5a28-412a-a4a5-56fe9320f969", "embedding": null, "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "89cad037-3cd0-40c1-a13d-48954b152a50", "node_type": "4", "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "112cf2c41b1e85acb4c3f9efa7214f83ad9c4cad253f587857bbbe21b74217c1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So first you make a design what your procedure is going to do, what your software is going to do, come up with some design and that design you convert into a program. Then what do you call the person who do designing? Whether the programmer does that? Yes if programmer is capable of doing that he can do it otherwise the person who will do this one should have the domain knowledge, the problems knowledge, knowledge about the problem and its solution.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 453, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "18ce4f7a-df99-453d-99da-11eb8c7871fb": {"__data__": {"id_": "18ce4f7a-df99-453d-99da-11eb8c7871fb", "embedding": null, "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "84eb41fb-4b99-42fb-8263-6b6773e745e7", "node_type": "4", "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "04bef88b5f0dcaff0f1f1e55bb08395099257accad5806c999de817df90386d1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He can give a solution, the one who understands the problem and has the domain knowledge. Suppose you are developing a software or an algorithm or a program for account then accountant can understand accounting better. Or if you are writing an application or developing an application for hospital then doctors or the administrative staff of a hospital can understand the system better than a programmer.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 404, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "271c89e4-8026-4f78-86c1-be203cc909d7": {"__data__": {"id_": "271c89e4-8026-4f78-86c1-be203cc909d7", "embedding": null, "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d944846f-285b-4641-8524-1486d47b3bb7", "node_type": "4", "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "hash": "058e4c8e74d62b468c090a559f11923b8291d2249dee5646df7668d434b49533", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So mostly the one who is having a knowledge about the domain for which the application is being developed, they can write on the algorithm. So the person who writes should have domain knowledge. Who will write the programmer? Programmer? So programmers can also have domain knowledge. They can also have domain knowledge that he is acting as a designer and here he is acting as a programmer. Next, what is the language used for writing algorithm? You can use any language.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 472, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e857c5e1-c8cf-490c-b178-c311a49d764b": {"__data__": {"id_": "e857c5e1-c8cf-490c-b178-c311a49d764b", "embedding": null, "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "34a2394e-a459-45d5-ba17-748ca422dd7e", "node_type": "4", "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "hash": "904eb1ca5e8cbc9017747b0ffa8acc872e73a567681c9265506ba9d1e7ecd56a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Any language, any language means English like language or you can use some mathematical notations. If you're writing English like language it will be like writing paras or samari but don't try it. If you want you can use mathematical notations also. It's better if you use mathematical notations. So any language can be used, any language means English language or some mathematical notations can be used as long as it is understandable by those people who are using it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 470, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "de137a35-b74f-46d1-b740-1bf206c45e6b": {"__data__": {"id_": "de137a35-b74f-46d1-b740-1bf206c45e6b", "embedding": null, "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b1e61df7-4462-4b84-a4ed-8c78ef71a548", "node_type": "4", "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "hash": "6cb306b48dd708e5deb5b3abaafa45f1c4982f208e7cf7576ca213864b34f035", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Like if a designer has written an algorithm then the programmer will be writing a program for it. So designers should understand and also programmers should understand that one. So the team of programmers who are working on that project should be able to understand it. Then this is written only using programming language like C++, Java, Python. So you can use different languages for developing a program. The next one more important thing here", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e6ef2133-5297-4064-b535-9ad1a56eeae9": {"__data__": {"id_": "e6ef2133-5297-4064-b535-9ad1a56eeae9", "embedding": null, "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "734ac39c-3d14-4486-a55b-0f3b3ca8197b", "node_type": "4", "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "hash": "75652f5626d3a034b092bfb06b8b469a8cc8e8192c64daa9dbfa68c5bc1fd114", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "When you write an L-Core, it's going to be hardware and software means operating system independent. It's not dependent on hardware, what machine you are going to use, what's the configuration of the machine and what's the operating system, either Linux operating system or Windows operating system, we don't bother about it. But when you write a program, it is dependent on hardware and operating system.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 405, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c77759df-7595-436f-bb49-bf279e04bf1e": {"__data__": {"id_": "c77759df-7595-436f-bb49-bf279e04bf1e", "embedding": null, "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4e7531b9-84ba-4055-8863-8f9a285a4613", "node_type": "4", "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "427c86b713e89ea116f9463b2a026e9bb592501861bb45c70c297ce3d7dc53dc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So when you develop the program you have to select some hardware on which you are going to run and also you have to select some operating system. You may be doing it for Linux or you may be doing it for Windows. So the method will be different and the environments are different. So this is important.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 301, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "47b0f35a-fb57-4275-8546-a68ded99e7f5": {"__data__": {"id_": "47b0f35a-fb57-4275-8546-a68ded99e7f5", "embedding": null, "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6d053aa8-8e93-4ec6-a42d-ec95a03408cc", "node_type": "4", "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "hash": "398adcfe82df433050774ab48668f1483369eeb41352f55468c2daf162e8eefb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Then next, after writing an algorithm we analyze, analyze an algorithm. Means we will study the algorithm to find out are we achieving the results perfectly or not and our algorithm is efficient or not in terms of time and space. We will see what does it mean by analysis. So we will analyze an algorithm. What do we do with the program? So you don't have to study the program. All the program is there. Just run it and check it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 429, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a519b0aa-d56a-4efc-9515-674dbb7e5295": {"__data__": {"id_": "a519b0aa-d56a-4efc-9515-674dbb7e5295", "embedding": null, "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c6cc0bff-ef7d-4a73-86c2-befb834c0fe5", "node_type": "4", "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "hash": "e22c92de10c79ced1f48786f7245fcf31b574c60484e8fe401ef8b54c4ab6634", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So we do testing, testing of the program. That's it. These are few differences between algorithm and program. These differences will help you understand what are algorithms. All right? Now one more thing I will tell you. The syntax of the language that we use, any language can be used but who actually write algorithms and who use them and who write the programs of programmers who are coming from university", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 409, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d1871840-13e0-4a46-bc21-c6ed5da00d19": {"__data__": {"id_": "d1871840-13e0-4a46-bc21-c6ed5da00d19", "embedding": null, "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f13c080a-aadc-46fe-94b9-c91022f2fecb", "node_type": "4", "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "hash": "611977a7f35968dc31b638dc4ad4a8e586ab079f13855a5d4cbb0da874f60956", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "mostly university graduate knows at least C language. So nowadays mostly in the name of algorithm we write C language program only. So the benefit is that the advantage that everybody knows C language now. So instead of writing some other language to confuse we can use C language only so that everybody can understand. So even at a school level C language is being taught so everybody is familiar with C language. So I may be using C language syntax for writing algorithm.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 473, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}, "docstore/metadata": {"f0de9d96-6635-42bc-8170-3bcec920365b": {"doc_hash": "c430f211ec764bdcae76900c4e16697542b4c20511f8a6e85a9c4eb39b8909ac", "ref_doc_id": "173f716b-af68-48f9-89d4-d5f15beb9b60"}, "6eac2685-ee4f-4fef-9d9a-150f50affaa0": {"doc_hash": "4ad1c6bdd83bf205fa237e96a52302ed33f8398c587da5be9a1a8ded122b2e34", "ref_doc_id": "fcf72281-9f80-40f4-80ce-98225378d6cf"}, "897fab3c-65f9-49a8-a82a-b58efd74d844": {"doc_hash": "9de25e42ac44b14202281b8dae5fe4ab91c9cd53c691e38b4d6cd11c3bd2905f", "ref_doc_id": "b2243f3b-762f-4332-a87a-3665833e5071"}, "a8e87d2b-9867-4383-90f6-87c809c26374": {"doc_hash": "1194a6613faaeb0a7a878895e81182794c6e44b2574f5abc3e763ef91df657c3", "ref_doc_id": "81512a0a-9787-4d40-ad6d-18d35944d935"}, "f90b7267-20b2-462f-a551-b83f3fe77d06": {"doc_hash": "1266e39a5d4f42f55827c97085e7d7a15e0a65ada22276c3bcca0eef40680949", "ref_doc_id": "93d76cb3-f1fb-4d31-a52f-e525cf96b8fd"}, "8adefc4b-4374-42fc-a2fa-c4db772c462a": {"doc_hash": "d6ef486336f72fd7c94bab14f62bae8101fbafdcb24b7ef6368225bb1ee3b1f5", "ref_doc_id": "df81dd36-d6cc-4649-bfe9-b5a538e15aa9"}, "d9001df7-4bdc-4eca-b9cf-f75fe70b9cf5": {"doc_hash": "1a62707754dbdba199310aa680aac7024b3e859ffee580073ab3558a663efd0e", "ref_doc_id": "14041998-c18f-45bc-9977-206bc348e963"}, "68eaaf37-d65a-4672-bd4b-990a934125de": {"doc_hash": "12b72f14b15db17716a930ec2b3d2a113630ee1670a79c3ec3b960847efe2cef", "ref_doc_id": "ab3b18df-a59f-44db-9fce-a361da9babd6"}, "13b34f12-ae56-485b-8ec7-2f6b2d61c68a": {"doc_hash": "c5e28599e9280bda0b28bd4f44b88775f9cdbc7383329d3f4616ad07cf393d28", "ref_doc_id": "5bc6d85a-049a-4434-8b09-fd457b9ae5bd"}, "d8c687fd-c2b9-4f34-9b73-2cca408d810f": {"doc_hash": "0f2bfa49416af5c8f43ec021a10b7d6f4d6034a415f7e6ecc51d1b3fc196d572", "ref_doc_id": "aaae7942-97bd-4734-8f4e-944963657b0e"}, "961f7c06-eb20-410b-8707-570369df8a50": {"doc_hash": "042d0c5661055c361ac474bc0a66398f9dfec1ef8821409527ffc8f3f7085115", "ref_doc_id": "1a2d96f8-7c60-4da4-a496-69908e0267c1"}, "c84363a2-a21a-42f1-97c3-99842e44a8ea": {"doc_hash": "6c0cf1da0adc53c56ad76abafc742a8c9588ea2126b6260b89ae31f41b3e4c0c", "ref_doc_id": "4bf3fb8e-6262-4f75-a0f4-b6796a36797a"}, "daa75629-0d4b-48a7-a1d5-9ccb50621a6a": {"doc_hash": "6d60b2f115e243a490078036aa920d74db02d8d8b545de8ec301130953350590", "ref_doc_id": "eca7c58f-59ab-4c92-8d5f-7b8532d0c4b8"}, "74cc2355-8293-42ca-a3f3-efa2dfc824db": {"doc_hash": "391414b3decc896a815cd84b2129c60099652efebfea5e52655bc5b95ffb32fe", "ref_doc_id": "56c26ab2-fa9d-4a16-b027-79f26ec13959"}, "696c3a2c-5de4-4e71-8d47-6ad6031cd32e": {"doc_hash": "86c0d3bfa3c69ce49d4c066fe61e575cd6ea88d278f93e74b8d014a755157049", "ref_doc_id": "25bdc5e9-9129-4af3-aaa6-8f4a777a7916"}, "974bfc19-ef0a-4c4d-acce-aaa438c54813": {"doc_hash": "598a6095632b0b45dc8c5090d579165ed121d20727eecac8ae2dd46132c82c01", "ref_doc_id": "81d5536f-f5f1-4a8a-a608-2408e5436d53"}, "0c578996-ff2a-461e-9271-15aace54ad63": {"doc_hash": "361862c0f977454a9199e3e48a15071f17d11e9701fc0d58e596a2aa9b2ccf82", "ref_doc_id": "f6ea6583-5e20-4063-a491-1be5c4acd1ec"}, "a55b29a3-7099-4c46-9710-cb70dbe5107e": {"doc_hash": "4d7a1162cb93cc1ddf90f1cbaa63e493c4b99ea4340852f2366ffc3065e4dbd0", "ref_doc_id": "42a03687-699e-4447-aed4-ef50253c32de"}, "719bc6d6-c62e-4bca-b191-bb64fcc1f366": {"doc_hash": "ec88fbfdfbb20e86dbdb39d217a8df6b13bcffd567f28af97dfb2b88a33bdafe", "ref_doc_id": "e1b4a5a2-a37b-4aa7-80fa-bc0a1a058707"}, "1d87cd85-7b1e-4323-a2cb-baf69f0097f6": {"doc_hash": "4a7ff22f59b74ae00b39ab65c06df98010df828f0b0b4f73907cce1a5ff5e256", "ref_doc_id": "cf03b2e5-6aba-4d37-9d0a-c968e0fdf048"}, "143d4837-b719-4565-8c79-542177e89c86": {"doc_hash": "34f74bec1826454cdb578a2b93e65d0a6622adc986e579b2b90f9630bace0253", "ref_doc_id": "5208887a-0aa6-44b5-a5ce-1c139e1f59b6"}, "0cc913f7-381d-4d76-b1f3-36c2e5463196": {"doc_hash": "79f3323b887004dcd12ecf4ed33ad6b6eba116e5683145bc59984c8eb1a643f7", "ref_doc_id": "3d5bc187-db46-4ca1-9070-d133c8ac652e"}, "46641d6f-2918-48ba-8dfb-a5442603dd81": {"doc_hash": "60b15fc5021301e78ef4722e38e354aa6d5d85fabee94f96b2e71d1a70622fa1", "ref_doc_id": "cc4d06c9-2dc0-44d2-80e4-4b022e2986cb"}, "55d0ebae-ba31-4cb2-9178-eef1ae71467c": {"doc_hash": "c430f211ec764bdcae76900c4e16697542b4c20511f8a6e85a9c4eb39b8909ac", "ref_doc_id": "6c416079-407e-4fa4-900f-54a86c740742"}, "864354d6-04ad-4f52-a4d2-735c27bddf3e": {"doc_hash": "4ad1c6bdd83bf205fa237e96a52302ed33f8398c587da5be9a1a8ded122b2e34", "ref_doc_id": "1c91213c-db08-41b7-8ae0-f2339c97c99a"}, "81115b5b-6637-4e39-a835-27f6b1f46ce9": {"doc_hash": "9de25e42ac44b14202281b8dae5fe4ab91c9cd53c691e38b4d6cd11c3bd2905f", "ref_doc_id": "9daa5958-aac1-48c6-bfb2-b8942123b14d"}, "79eceba8-3a70-4b23-badd-dc4e3ea6166c": {"doc_hash": "1194a6613faaeb0a7a878895e81182794c6e44b2574f5abc3e763ef91df657c3", "ref_doc_id": "8600a13c-939a-4a6b-a105-90add69b5dbb"}, "db6ebbaa-0c3f-4856-a72c-f867f63bf343": {"doc_hash": "1266e39a5d4f42f55827c97085e7d7a15e0a65ada22276c3bcca0eef40680949", "ref_doc_id": "aee6e897-92e3-44fa-93c3-44eed568f9b7"}, "f904c5ba-5b44-4384-a5cc-aa8ed0740c79": {"doc_hash": "d6ef486336f72fd7c94bab14f62bae8101fbafdcb24b7ef6368225bb1ee3b1f5", "ref_doc_id": "75ca3661-3df4-4ca4-a540-d26f5b4d7098"}, "f0ec30ba-37f5-4e64-8856-1637703a31ef": {"doc_hash": "1a62707754dbdba199310aa680aac7024b3e859ffee580073ab3558a663efd0e", "ref_doc_id": "006505af-f1c0-4d87-81e9-5dcdd89173ce"}, "aeb298ae-0fbb-4c0a-bd24-e4e271b98b6b": {"doc_hash": "12b72f14b15db17716a930ec2b3d2a113630ee1670a79c3ec3b960847efe2cef", "ref_doc_id": "db95727f-89e6-4d3a-8205-3ecf4e152900"}, "c92091f0-f7d6-4592-84c0-d2d3f8f66e8e": {"doc_hash": "c5e28599e9280bda0b28bd4f44b88775f9cdbc7383329d3f4616ad07cf393d28", "ref_doc_id": "5f739082-1605-416c-85c8-e4394574fb33"}, "49d77cf4-8e45-4351-baea-87c204e60fd2": {"doc_hash": "0f2bfa49416af5c8f43ec021a10b7d6f4d6034a415f7e6ecc51d1b3fc196d572", "ref_doc_id": "a5ae1367-ed5c-420a-b7bb-3ebcc80cf13d"}, "0e90d3a0-30ec-472d-a88d-50918e642470": {"doc_hash": "042d0c5661055c361ac474bc0a66398f9dfec1ef8821409527ffc8f3f7085115", "ref_doc_id": "74e4baed-24b0-4770-898e-2cb2471e1dc1"}, "7683b1e1-bd5c-4c24-8440-f32a1559249c": {"doc_hash": "6c0cf1da0adc53c56ad76abafc742a8c9588ea2126b6260b89ae31f41b3e4c0c", "ref_doc_id": "8e7819d6-14c5-4259-ab94-2ae78e4f6534"}, "ba7dd449-903c-46da-9a32-b34ac4fe4aa7": {"doc_hash": "6d60b2f115e243a490078036aa920d74db02d8d8b545de8ec301130953350590", "ref_doc_id": "92108841-daef-45b2-81f9-b90b043534f8"}, "d6291acd-5a28-412a-a4a5-56fe9320f969": {"doc_hash": "391414b3decc896a815cd84b2129c60099652efebfea5e52655bc5b95ffb32fe", "ref_doc_id": "89cad037-3cd0-40c1-a13d-48954b152a50"}, "18ce4f7a-df99-453d-99da-11eb8c7871fb": {"doc_hash": "86c0d3bfa3c69ce49d4c066fe61e575cd6ea88d278f93e74b8d014a755157049", "ref_doc_id": "84eb41fb-4b99-42fb-8263-6b6773e745e7"}, "271c89e4-8026-4f78-86c1-be203cc909d7": {"doc_hash": "598a6095632b0b45dc8c5090d579165ed121d20727eecac8ae2dd46132c82c01", "ref_doc_id": "d944846f-285b-4641-8524-1486d47b3bb7"}, "e857c5e1-c8cf-490c-b178-c311a49d764b": {"doc_hash": "361862c0f977454a9199e3e48a15071f17d11e9701fc0d58e596a2aa9b2ccf82", "ref_doc_id": "34a2394e-a459-45d5-ba17-748ca422dd7e"}, "de137a35-b74f-46d1-b740-1bf206c45e6b": {"doc_hash": "4d7a1162cb93cc1ddf90f1cbaa63e493c4b99ea4340852f2366ffc3065e4dbd0", "ref_doc_id": "b1e61df7-4462-4b84-a4ed-8c78ef71a548"}, "e6ef2133-5297-4064-b535-9ad1a56eeae9": {"doc_hash": "ec88fbfdfbb20e86dbdb39d217a8df6b13bcffd567f28af97dfb2b88a33bdafe", "ref_doc_id": "734ac39c-3d14-4486-a55b-0f3b3ca8197b"}, "c77759df-7595-436f-bb49-bf279e04bf1e": {"doc_hash": "4a7ff22f59b74ae00b39ab65c06df98010df828f0b0b4f73907cce1a5ff5e256", "ref_doc_id": "4e7531b9-84ba-4055-8863-8f9a285a4613"}, "47b0f35a-fb57-4275-8546-a68ded99e7f5": {"doc_hash": "34f74bec1826454cdb578a2b93e65d0a6622adc986e579b2b90f9630bace0253", "ref_doc_id": "6d053aa8-8e93-4ec6-a42d-ec95a03408cc"}, "a519b0aa-d56a-4efc-9515-674dbb7e5295": {"doc_hash": "79f3323b887004dcd12ecf4ed33ad6b6eba116e5683145bc59984c8eb1a643f7", "ref_doc_id": "c6cc0bff-ef7d-4a73-86c2-befb834c0fe5"}, "d1871840-13e0-4a46-bc21-c6ed5da00d19": {"doc_hash": "60b15fc5021301e78ef4722e38e354aa6d5d85fabee94f96b2e71d1a70622fa1", "ref_doc_id": "f13c080a-aadc-46fe-94b9-c91022f2fecb"}}}