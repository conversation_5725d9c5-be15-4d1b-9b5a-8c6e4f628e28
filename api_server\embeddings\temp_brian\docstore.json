{"docstore/metadata": {"2aba7153-a41d-4184-aa63-0008e4133955": {"doc_hash": "abea6f58cb107b083b5ea2baaad8a7ab578746a298512c9a8e902d50b7748f15"}, "e93c3af0-1d7b-4ff5-adf6-1a494956c1e8": {"doc_hash": "6b42efbc5c8b10eb959400b2a51a0da82393d17370697dd6feac299303dd6a98"}, "bb5a72b1-e5d6-4ca4-8a66-ab30791f1d84": {"doc_hash": "ef346cb5ae3cf7d8aa1b4326acd0439e194fa2896aaf0a5ff1be5152f3386f47"}, "cd7bc4a5-ed58-4cd8-a8b9-ef4cf02ec864": {"doc_hash": "d9de66701629057284cc1334d18234fa65996743a5f896485ca0a0db1f4c0f66"}, "16f079a9-d376-47ae-ac2e-6c96ea3c5e7b": {"doc_hash": "c7d143b1e17ce454edb8db644d0457812dadd290995b5788b91ca294b6471ab2"}, "5097bf99-a766-4963-995d-bbcdbf3d2604": {"doc_hash": "ce1a4652d723fd6d1a46007fc1a0d517fd444c966d50a66863f685a4d5b0ac99"}, "871c737d-2f53-4795-b927-8771cc087290": {"doc_hash": "cd7c4e93dd1f53fd3556d536c797142e9f5fe9dbdc51008f789dc94bbd141119"}, "8b9da95a-e24a-462e-a8a9-b1deabdc680f": {"doc_hash": "554da75241ee23d26d8ef29c3e4900645c4060e295208c011409ce08199729cf"}, "f9170471-51e9-45bd-a99c-b4d9a80dc533": {"doc_hash": "b4df039fdf4b65bde2c0e6d293c591232a029792b3dd515508b349597d77afef"}, "081b62b7-c77c-4de1-a0c5-d4b4a607f2c9": {"doc_hash": "c8491fcedd9ce0de205289516f55fefb6b29d33ee5573e0b5b61af39a3856262"}, "fd2d6cd7-dcbc-49d8-8d3a-aff857b3e30f": {"doc_hash": "bb119671b79e61d09ca0ca91db1e1571f5e267df0e8c017ba33d85db929eb25e"}, "0b070a7a-a4c0-4554-a7fd-b7102d705b65": {"doc_hash": "0ba869863c7fa0ab2c4700b6c21515060d76138020045c940ded89d894b2cb69"}, "9fd09855-435a-44d5-a61d-61d5662819a6": {"doc_hash": "90f7981e9f182ca1e53987d4970f08f5422528a486b76d93791c0d202bd33739"}, "0f734f30-9135-4eae-b5c8-3d9b74c3b2ff": {"doc_hash": "ad220ea7e13003651e67e50a40eb060390290008aafe6c0ce7e367755e2d8f16"}, "8ca51b17-ece2-4bf4-ac37-c36ee91ea04c": {"doc_hash": "b0e03a034ecd78eec7b4cb691e9711f2acf4a788a9c5f1698306044accf4918f"}, "f059ae34-2f2d-4157-ad3f-8f33373183e1": {"doc_hash": "22b0a3804e8dd55b19204e89c57128f2ceb46d953837067a67b288ed84fca199"}, "586c9e35-a1cc-4310-a90d-c38e4c541bf1": {"doc_hash": "9e56d6360c1c491ff5ee831acee4d6ab419d4f12e92dde1e5a8a38548b7014d8"}, "d629396f-04c1-4e41-b1a9-f981897ef1f4": {"doc_hash": "d9ef7973887aae12e98145641682049dac7f2f57b5a0ee11de225321784a57ea"}, "38b86218-6666-4ee8-a536-1aaf6edea449": {"doc_hash": "68cc4e89408fa93ebb71e095c25de55184bcda47e3caff593b2c163ef1ab1382"}, "f57d1161-1c8b-449c-ae99-e75172ac83cd": {"doc_hash": "a1670d40697779c690a428dcaeed00b9f799049a0fdc5b32057f32c8dbf94942"}, "da0e8d8f-a4eb-45aa-a8ed-4fc00a6a56d8": {"doc_hash": "652e504e2c876308e23c9a83b16dd54f0623c755426c532117866aceba216047"}, "9e404a5f-49e5-49e2-a63b-4c88f79acc40": {"doc_hash": "24e380aab4603c61a6258a7ed40f0d2c7646acb35ab6b2721a07e76ba20643e6"}, "1d205454-5011-4033-aeea-52c2cfc6ad25": {"doc_hash": "6a617750885f1e8eb937b19d5c8283d8d62142dc28348d1e2b6c62162dc08336"}, "dfcb403f-b5e4-4e78-a110-23f7c23674c3": {"doc_hash": "1979adbd4a59205c4f0bc4bfafcc4021e5bd4cd0aff0a5ce84b6060b4114f51c"}, "9ea26817-f414-45dd-bc5e-ed6330341c66": {"doc_hash": "aad00aff3dfb2c2d409affb12a1661374d02b345f7d17e771f15135123467536"}, "ee561a68-171d-4db9-ba73-397cb3b67f1b": {"doc_hash": "a45ac9b85e9a4f7af68cd2dcabaee2e16e7f1789b0cb4ac533e143b95bb65ffd"}, "b4c888a3-5542-41b4-abbe-36138ca53be5": {"doc_hash": "743c8a2087861b224fe1dbaac243f06e615b07b4ada2db0a9c0d9318f59c42c1"}, "2467e199-f6b1-4c06-bdf4-7fec0e28779d": {"doc_hash": "fe85c20ac2b6e5c66ac057ca7917a1689c3ed18b4b35c1d9dce0ee35a73f1931"}, "39fe741f-1a41-4544-9113-7b4542313e1a": {"doc_hash": "cce921104d576a51d854a3b564c50da0177265aefc513629894c46b95ed777b8"}, "bebf48c6-074d-4b04-96f1-e1411ecfc44b": {"doc_hash": "972c80e2d48397f8932a21ce32cc09e0cf9b40a3620517871523da6276840a8c", "ref_doc_id": "2aba7153-a41d-4184-aa63-0008e4133955"}, "352153dc-1831-4d06-8abf-1db8806a9e87": {"doc_hash": "d59123401b499ad0aff1ceea4629c2873c1ac8c7e45fb4287e178dceb7384159", "ref_doc_id": "e93c3af0-1d7b-4ff5-adf6-1a494956c1e8"}, "9496dccb-7dc6-430b-ae6d-184529ececd2": {"doc_hash": "8306e97cdcd00644f11d38cf865be9768ec6cc5fe4e2b7d7ecc4430ef53585a6", "ref_doc_id": "bb5a72b1-e5d6-4ca4-8a66-ab30791f1d84"}, "2b0e1d48-7755-4a9c-83d7-aa46ffa0379c": {"doc_hash": "354aa61414d4a4b8bfbdd521621e648db784dcce6716a8401a60b71fa4ada894", "ref_doc_id": "cd7bc4a5-ed58-4cd8-a8b9-ef4cf02ec864"}, "f7c5d764-2d8f-430a-818b-d826b2501adf": {"doc_hash": "7bb5c89dc1965b2407ed892a0ff1200bec73137a45f9da276e4ad45052f981c2", "ref_doc_id": "16f079a9-d376-47ae-ac2e-6c96ea3c5e7b"}, "dad09704-b7ec-4e6a-9cdc-da8c439f3f9d": {"doc_hash": "29f53781c51473c99846af4a439306ecafc6d42fd84bf28d0414e472c2106b5d", "ref_doc_id": "5097bf99-a766-4963-995d-bbcdbf3d2604"}, "792bf6cb-e40b-49d3-a2c6-2a72c75cf081": {"doc_hash": "6350cdd96b6fbf2e6b76e3442aa87dccc8d09275d15b9579ac0b21ee97335035", "ref_doc_id": "871c737d-2f53-4795-b927-8771cc087290"}, "c16804fd-50b0-474c-9fc6-d221a01350ff": {"doc_hash": "b1a513c305f73b2e4ed6f88af58a0e333314454786e54bf8e59d2314a805acab", "ref_doc_id": "8b9da95a-e24a-462e-a8a9-b1deabdc680f"}, "a050b997-aa60-40ed-b2a8-7f45dddc7429": {"doc_hash": "dab96e4cc8214a68e3d003a8cccce2e693e10de099544b5be951f89ac3eeaf55", "ref_doc_id": "f9170471-51e9-45bd-a99c-b4d9a80dc533"}, "654bfda8-0de0-4407-92a1-72895b8a27c7": {"doc_hash": "9af17455ae98f0c4ba95a28fead136bc075fefc2cc7aa78c6e994ea9b19813ab", "ref_doc_id": "081b62b7-c77c-4de1-a0c5-d4b4a607f2c9"}, "ea5dabe4-0ec0-4fd0-ac46-24e892485fc5": {"doc_hash": "5010824b2bb5ad208b022a85f503a1e45ded181ce6d3df6c921267d25d796039", "ref_doc_id": "fd2d6cd7-dcbc-49d8-8d3a-aff857b3e30f"}, "a828abf5-0438-4f0d-84c2-b2ea383c70b9": {"doc_hash": "1fc437b935e0b51e2d1eb420d58f81cf1830d84d15d34e8607d123a97cd23c49", "ref_doc_id": "0b070a7a-a4c0-4554-a7fd-b7102d705b65"}, "9d30c286-c7d5-42be-a0b9-dc3cd2f324cf": {"doc_hash": "7e29742794fc49f74d4bd1ec7f35835e8807f6a63d3f2b4de666b1c65ad96a5e", "ref_doc_id": "9fd09855-435a-44d5-a61d-61d5662819a6"}, "aa4504c0-019c-4cde-8801-d74970a09b9a": {"doc_hash": "6fc618ca9a968ff7243c17939759940eef32922dbab83b7de59b3f1d6458f547", "ref_doc_id": "0f734f30-9135-4eae-b5c8-3d9b74c3b2ff"}, "765bdb71-91cb-4359-af7e-dae352e74a30": {"doc_hash": "edb0d78b7a70ab4fea6d55307a6a92d905e957135f223327824876dbe0c21131", "ref_doc_id": "8ca51b17-ece2-4bf4-ac37-c36ee91ea04c"}, "9b4b71fa-d963-44d8-9587-8a87402fbe84": {"doc_hash": "775374fd34a02993cfd4bc9c0e3748fa61d2c05fa654789f264b86edb0409151", "ref_doc_id": "f059ae34-2f2d-4157-ad3f-8f33373183e1"}, "1597899a-c542-48e9-9dc3-e183aa5172d2": {"doc_hash": "ef29a74bf827c8b537f972177469f8c91bd7c1b8e0803031115e27b0d1a07113", "ref_doc_id": "586c9e35-a1cc-4310-a90d-c38e4c541bf1"}, "831b4b65-5f76-4697-9866-8336f1217595": {"doc_hash": "48bcdf8385395757a0a49b92d9669aa95b98c380d2763d78465fb4b5b1a127a7", "ref_doc_id": "d629396f-04c1-4e41-b1a9-f981897ef1f4"}, "3813f612-3fe8-49a8-920b-53e76c2ed13d": {"doc_hash": "f3d9fca8aa25fe2e8e4075216f3b4c2cf24f304fd6bc3b6dba18002d1b7d45aa", "ref_doc_id": "38b86218-6666-4ee8-a536-1aaf6edea449"}, "3ca9c099-c7d3-472b-a2e1-631d44f61857": {"doc_hash": "a1e7dda825987040c9ccba55c682fa0d1e6dc011cc24bbf9fccba2d0476211bf", "ref_doc_id": "f57d1161-1c8b-449c-ae99-e75172ac83cd"}, "81d554cf-dd4e-4314-b575-453b1e75e4e9": {"doc_hash": "f63618686b75a1d73ed0d27801fd7d1a26c1541d0f6eaf3e57768fba1c5cf269", "ref_doc_id": "da0e8d8f-a4eb-45aa-a8ed-4fc00a6a56d8"}, "fce4c0b4-66b1-4fe9-92c7-8c287c02879c": {"doc_hash": "3496eceff30d7bdbcc7a9f371dbae0f1cf61e4ca43d4c3ea62ca7b1098752ef6", "ref_doc_id": "9e404a5f-49e5-49e2-a63b-4c88f79acc40"}, "f978ddfd-8cca-45b5-a579-505959308672": {"doc_hash": "2707fa8c0e4c929de36ff60820a052ba3f028e75debf7e39c560f65722d32d64", "ref_doc_id": "1d205454-5011-4033-aeea-52c2cfc6ad25"}, "35e82b25-0571-4d15-9ad2-450170ce2963": {"doc_hash": "48ebf53b38dea79562097ce985849e9577ffd1783bf811e1af24be790e375b08", "ref_doc_id": "dfcb403f-b5e4-4e78-a110-23f7c23674c3"}, "64b07a1e-496c-48e8-b06a-54a3d4409476": {"doc_hash": "11ec1d2f2ed78f930b3cb4415facd121b7e2c63100269ec93a6f9ef6704bb5f7", "ref_doc_id": "9ea26817-f414-45dd-bc5e-ed6330341c66"}, "95969d7c-5726-45b3-b95b-2b5972bc9cee": {"doc_hash": "b2e915289df40f960c900b621efc6534e0b14768c6eb92979edfbfe111347747", "ref_doc_id": "ee561a68-171d-4db9-ba73-397cb3b67f1b"}, "5f52086f-1d5d-4646-8509-25c125f4fdbb": {"doc_hash": "8d7b7659c6865fb5ee10b92953e9b2e8c2f753a92003f6d12796a4204530e629", "ref_doc_id": "b4c888a3-5542-41b4-abbe-36138ca53be5"}, "073a1e7f-4d2d-4a2c-8cac-8e9faca24106": {"doc_hash": "10dc7b430ee69853ba3be08dcc636ed0e24785e294ce69534e8790eec39059b8", "ref_doc_id": "2467e199-f6b1-4c06-bdf4-7fec0e28779d"}, "570e4acb-e820-4fbe-b763-534788fbc274": {"doc_hash": "ab9c592ca0219afa4376a86f601372336c7472e98792711507545d39afc65cd0", "ref_doc_id": "39fe741f-1a41-4544-9113-7b4542313e1a"}}, "docstore/ref_doc_info": {"2aba7153-a41d-4184-aa63-0008e4133955": {"node_ids": ["bebf48c6-074d-4b04-96f1-e1411ecfc44b"], "metadata": {"start_time": 0.0, "end_time": 18.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "e93c3af0-1d7b-4ff5-adf6-1a494956c1e8": {"node_ids": ["352153dc-1831-4d06-8abf-1db8806a9e87"], "metadata": {"start_time": 19.68, "end_time": 40.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "bb5a72b1-e5d6-4ca4-8a66-ab30791f1d84": {"node_ids": ["9496dccb-7dc6-430b-ae6d-184529ececd2"], "metadata": {"start_time": 40.98, "end_time": 58.96, "file_path": "transcript.json", "video_id": "temp_brian"}}, "cd7bc4a5-ed58-4cd8-a8b9-ef4cf02ec864": {"node_ids": ["2b0e1d48-7755-4a9c-83d7-aa46ffa0379c"], "metadata": {"start_time": 59.5, "end_time": 72.44, "file_path": "transcript.json", "video_id": "temp_brian"}}, "16f079a9-d376-47ae-ac2e-6c96ea3c5e7b": {"node_ids": ["f7c5d764-2d8f-430a-818b-d826b2501adf"], "metadata": {"start_time": 72.72, "end_time": 91.12, "file_path": "transcript.json", "video_id": "temp_brian"}}, "5097bf99-a766-4963-995d-bbcdbf3d2604": {"node_ids": ["dad09704-b7ec-4e6a-9cdc-da8c439f3f9d"], "metadata": {"start_time": 91.34, "end_time": 115.28, "file_path": "transcript.json", "video_id": "temp_brian"}}, "871c737d-2f53-4795-b927-8771cc087290": {"node_ids": ["792bf6cb-e40b-49d3-a2c6-2a72c75cf081"], "metadata": {"start_time": 115.54, "end_time": 144.2, "file_path": "transcript.json", "video_id": "temp_brian"}}, "8b9da95a-e24a-462e-a8a9-b1deabdc680f": {"node_ids": ["c16804fd-50b0-474c-9fc6-d221a01350ff"], "metadata": {"start_time": 144.34, "end_time": 170.08, "file_path": "transcript.json", "video_id": "temp_brian"}}, "f9170471-51e9-45bd-a99c-b4d9a80dc533": {"node_ids": ["a050b997-aa60-40ed-b2a8-7f45dddc7429"], "metadata": {"start_time": 170.3, "end_time": 183.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "081b62b7-c77c-4de1-a0c5-d4b4a607f2c9": {"node_ids": ["654bfda8-0de0-4407-92a1-72895b8a27c7"], "metadata": {"start_time": 183.88, "end_time": 211.4, "file_path": "transcript.json", "video_id": "temp_brian"}}, "fd2d6cd7-dcbc-49d8-8d3a-aff857b3e30f": {"node_ids": ["ea5dabe4-0ec0-4fd0-ac46-24e892485fc5"], "metadata": {"start_time": 211.02, "end_time": 239.8, "file_path": "transcript.json", "video_id": "temp_brian"}}, "0b070a7a-a4c0-4554-a7fd-b7102d705b65": {"node_ids": ["a828abf5-0438-4f0d-84c2-b2ea383c70b9"], "metadata": {"start_time": 239.46, "end_time": 264.56, "file_path": "transcript.json", "video_id": "temp_brian"}}, "9fd09855-435a-44d5-a61d-61d5662819a6": {"node_ids": ["9d30c286-c7d5-42be-a0b9-dc3cd2f324cf"], "metadata": {"start_time": 264.78, "end_time": 291.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "0f734f30-9135-4eae-b5c8-3d9b74c3b2ff": {"node_ids": ["aa4504c0-019c-4cde-8801-d74970a09b9a"], "metadata": {"start_time": 291.86, "end_time": 314.34, "file_path": "transcript.json", "video_id": "temp_brian"}}, "8ca51b17-ece2-4bf4-ac37-c36ee91ea04c": {"node_ids": ["765bdb71-91cb-4359-af7e-dae352e74a30"], "metadata": {"start_time": 314.6, "end_time": 332.06, "file_path": "transcript.json", "video_id": "temp_brian"}}, "f059ae34-2f2d-4157-ad3f-8f33373183e1": {"node_ids": ["9b4b71fa-d963-44d8-9587-8a87402fbe84"], "metadata": {"start_time": 332.5, "end_time": 360.28, "file_path": "transcript.json", "video_id": "temp_brian"}}, "586c9e35-a1cc-4310-a90d-c38e4c541bf1": {"node_ids": ["1597899a-c542-48e9-9dc3-e183aa5172d2"], "metadata": {"start_time": 360.14, "end_time": 366.98, "file_path": "transcript.json", "video_id": "temp_brian"}}, "d629396f-04c1-4e41-b1a9-f981897ef1f4": {"node_ids": ["831b4b65-5f76-4697-9866-8336f1217595"], "metadata": {"start_time": 367.2, "end_time": 395.4, "file_path": "transcript.json", "video_id": "temp_brian"}}, "38b86218-6666-4ee8-a536-1aaf6edea449": {"node_ids": ["3813f612-3fe8-49a8-920b-53e76c2ed13d"], "metadata": {"start_time": 395.72, "end_time": 422.6, "file_path": "transcript.json", "video_id": "temp_brian"}}, "f57d1161-1c8b-449c-ae99-e75172ac83cd": {"node_ids": ["3ca9c099-c7d3-472b-a2e1-631d44f61857"], "metadata": {"start_time": 423.04, "end_time": 442.26, "file_path": "transcript.json", "video_id": "temp_brian"}}, "da0e8d8f-a4eb-45aa-a8ed-4fc00a6a56d8": {"node_ids": ["81d554cf-dd4e-4314-b575-453b1e75e4e9"], "metadata": {"start_time": 442.64, "end_time": 471.1, "file_path": "transcript.json", "video_id": "temp_brian"}}, "9e404a5f-49e5-49e2-a63b-4c88f79acc40": {"node_ids": ["fce4c0b4-66b1-4fe9-92c7-8c287c02879c"], "metadata": {"start_time": 471.32, "end_time": 497.38, "file_path": "transcript.json", "video_id": "temp_brian"}}, "1d205454-5011-4033-aeea-52c2cfc6ad25": {"node_ids": ["f978ddfd-8cca-45b5-a579-505959308672"], "metadata": {"start_time": 497.84, "end_time": 515.22, "file_path": "transcript.json", "video_id": "temp_brian"}}, "dfcb403f-b5e4-4e78-a110-23f7c23674c3": {"node_ids": ["35e82b25-0571-4d15-9ad2-450170ce2963"], "metadata": {"start_time": 515.44, "end_time": 537.56, "file_path": "transcript.json", "video_id": "temp_brian"}}, "9ea26817-f414-45dd-bc5e-ed6330341c66": {"node_ids": ["64b07a1e-496c-48e8-b06a-54a3d4409476"], "metadata": {"start_time": 537.52, "end_time": 546.44, "file_path": "transcript.json", "video_id": "temp_brian"}}, "ee561a68-171d-4db9-ba73-397cb3b67f1b": {"node_ids": ["95969d7c-5726-45b3-b95b-2b5972bc9cee"], "metadata": {"start_time": 546.94, "end_time": 572.84, "file_path": "transcript.json", "video_id": "temp_brian"}}, "b4c888a3-5542-41b4-abbe-36138ca53be5": {"node_ids": ["5f52086f-1d5d-4646-8509-25c125f4fdbb"], "metadata": {"start_time": 573.06, "end_time": 601.88, "file_path": "transcript.json", "video_id": "temp_brian"}}, "2467e199-f6b1-4c06-bdf4-7fec0e28779d": {"node_ids": ["073a1e7f-4d2d-4a2c-8cac-8e9faca24106"], "metadata": {"start_time": 602.12, "end_time": 629.9, "file_path": "transcript.json", "video_id": "temp_brian"}}, "39fe741f-1a41-4544-9113-7b4542313e1a": {"node_ids": ["570e4acb-e820-4fbe-b763-534788fbc274"], "metadata": {"start_time": 630.56, "end_time": 655.04, "file_path": "transcript.json", "video_id": "temp_brian"}}}, "docstore/data": {"bebf48c6-074d-4b04-96f1-e1411ecfc44b": {"__data__": {"id_": "bebf48c6-074d-4b04-96f1-e1411ecfc44b", "embedding": null, "metadata": {"start_time": 0.0, "end_time": 18.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2aba7153-a41d-4184-aa63-0008e4133955", "node_type": "4", "metadata": {"start_time": 0.0, "end_time": 18.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "abea6f58cb107b083b5ea2baaad8a7ab578746a298512c9a8e902d50b7748f15", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<PERSON><PERSON><PERSON>, so I don't know if anyone's seen the film, I think it's a masterpiece, <PERSON><PERSON><PERSON>. And I was interested in <PERSON><PERSON><PERSON>. I got interested in him as a character quite a long time ago because I discovered that he gave the BBC wreath lectures in 1953 and they've almost been obliterated from history because they're really hard.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 338, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "352153dc-1831-4d06-8abf-1db8806a9e87": {"__data__": {"id_": "352153dc-1831-4d06-8abf-1db8806a9e87", "embedding": null, "metadata": {"start_time": 19.68, "end_time": 40.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e93c3af0-1d7b-4ff5-adf6-1a494956c1e8", "node_type": "4", "metadata": {"start_time": 19.68, "end_time": 40.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "6b42efbc5c8b10eb959400b2a51a0da82393d17370697dd6feac299303dd6a98", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "One of the things I grew up watching was one of the people was <PERSON> who I don't know if anyone's old enough will remember I think <PERSON>'s cosmos and he was on the BBC 13 episodes 13 weeks and there's very little science and television at that point and three channels or something. But cosmos was on and <PERSON><PERSON>.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 321, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9496dccb-7dc6-430b-ae6d-184529ececd2": {"__data__": {"id_": "9496dccb-7dc6-430b-ae6d-184529ececd2", "embedding": null, "metadata": {"start_time": 40.98, "end_time": 58.96, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "bb5a72b1-e5d6-4ca4-8a66-ab30791f1d84", "node_type": "4", "metadata": {"start_time": 40.98, "end_time": 58.96, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "ef346cb5ae3cf7d8aa1b4326acd0439e194fa2896aaf0a5ff1be5152f3386f47", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "presented astronomy and you talk about astronomy in the solar system and the universe but he put it in a context the context of our civilization. And he was explicit that this way of thinking this way of interrogating nature trying to understand the natural world.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 264, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2b0e1d48-7755-4a9c-83d7-aa46ffa0379c": {"__data__": {"id_": "2b0e1d48-7755-4a9c-83d7-aa46ffa0379c", "embedding": null, "metadata": {"start_time": 59.5, "end_time": 72.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cd7bc4a5-ed58-4cd8-a8b9-ef4cf02ec864", "node_type": "4", "metadata": {"start_time": 59.5, "end_time": 72.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "d9de66701629057284cc1334d18234fa65996743a5f896485ca0a0db1f4c0f66", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Is vital for our survival as a species is it's out central to the is one of the one of the necessary foundations of civilization so is a polemic and.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 149, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f7c5d764-2d8f-430a-818b-d826b2501adf": {"__data__": {"id_": "f7c5d764-2d8f-430a-818b-d826b2501adf", "embedding": null, "metadata": {"start_time": 72.72, "end_time": 91.12, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "16f079a9-d376-47ae-ac2e-6c96ea3c5e7b", "node_type": "4", "metadata": {"start_time": 72.72, "end_time": 91.12, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "c7d143b1e17ce454edb8db644d0457812dadd290995b5788b91ca294b6471ab2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So I genuinely think that science has the thought process, the things we discover and that way of thinking about the world, acquiring reliable knowledge about the world is the way we do it. That's important. So I do have a", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 222, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "dad09704-b7ec-4e6a-9cdc-da8c439f3f9d": {"__data__": {"id_": "dad09704-b7ec-4e6a-9cdc-da8c439f3f9d", "embedding": null, "metadata": {"start_time": 91.34, "end_time": 115.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5097bf99-a766-4963-995d-bbcdbf3d2604", "node_type": "4", "metadata": {"start_time": 91.34, "end_time": 115.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "ce1a4652d723fd6d1a46007fc1a0d517fd444c966d50a66863f685a4d5b0ac99", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "an agenda when I talk about science on television or live shows or whatever it is because I think that it's important. So there is an underlying feeling there that I have that these, I said once, someone asked me once, why do you want to present a program on BBC One, for example, about astronomy?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 297, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "792bf6cb-e40b-49d3-a2c6-2a72c75cf081": {"__data__": {"id_": "792bf6cb-e40b-49d3-a2c6-2a72c75cf081", "embedding": null, "metadata": {"start_time": 115.54, "end_time": 144.2, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "871c737d-2f53-4795-b927-8771cc087290", "node_type": "4", "metadata": {"start_time": 115.54, "end_time": 144.2, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "cd7c4e93dd1f53fd3556d536c797142e9f5fe9dbdc51008f789dc94bbd141119", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And I said that I think science is too important not to be part of popular culture. So I really believe that. So if you believe it and you get a chance, look again, you get the chance, the platform to do it, then it would be ridiculous not to try and take that. I do think that scientists, if they want to and get the chance, have in some sense a responsibility to talk about it. Because you know, I mean, and there are obvious things we could talk about.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 455, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c16804fd-50b0-474c-9fc6-d221a01350ff": {"__data__": {"id_": "c16804fd-50b0-474c-9fc6-d221a01350ff", "embedding": null, "metadata": {"start_time": 144.34, "end_time": 170.08, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8b9da95a-e24a-462e-a8a9-b1deabdc680f", "node_type": "4", "metadata": {"start_time": 144.34, "end_time": 170.08, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "554da75241ee23d26d8ef29c3e4900645c4060e295208c011409ce08199729cf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "One topic, actually, is <PERSON><PERSON><PERSON>. So I don't know if anyone's seen the film, I think it's a masterpiece, <PERSON><PERSON><PERSON>. And I was interested in <PERSON><PERSON><PERSON>. I got interested in him as a character quite a long time ago because I discovered that he gave the BBC wreath lectures in 1953. And they've almost been obliterated from history because they're really hard. And so we tend to think of it. But in those lectures, when I found them,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 436, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a050b997-aa60-40ed-b2a8-7f45dddc7429": {"__data__": {"id_": "a050b997-aa60-40ed-b2a8-7f45dddc7429", "embedding": null, "metadata": {"start_time": 170.3, "end_time": 183.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f9170471-51e9-45bd-a99c-b4d9a80dc533", "node_type": "4", "metadata": {"start_time": 170.3, "end_time": 183.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "b4df039fdf4b65bde2c0e6d293c591232a029792b3dd515508b349597d77afef", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "I saw this scientist who obviously famously played a role in developing the atomic bomb, so in delivering the means by which we might destroy ourselves as a civilization, and he obviously knew that and it tortured him.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 218, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "654bfda8-0de0-4407-92a1-72895b8a27c7": {"__data__": {"id_": "654bfda8-0de0-4407-92a1-72895b8a27c7", "embedding": null, "metadata": {"start_time": 183.88, "end_time": 211.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "081b62b7-c77c-4de1-a0c5-d4b4a607f2c9", "node_type": "4", "metadata": {"start_time": 183.88, "end_time": 211.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "c8491fcedd9ce0de205289516f55fefb6b29d33ee5573e0b5b61af39a3856262", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So that what I made him do was think about how we might avoid doing that. So he started thinking about politics and society and civilization. And are there any lessons from this tremendously successful approach to requiring reliable knowledge, which we call science? Are there any lessons that we could apply in wider society? Certainly wasn't saying that scientists should run the world, right?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 395, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ea5dabe4-0ec0-4fd0-ac46-24e892485fc5": {"__data__": {"id_": "ea5dabe4-0ec0-4fd0-ac46-24e892485fc5", "embedding": null, "metadata": {"start_time": 211.02, "end_time": 239.8, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fd2d6cd7-dcbc-49d8-8d3a-aff857b3e30f", "node_type": "4", "metadata": {"start_time": 211.02, "end_time": 239.8, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "bb119671b79e61d09ca0ca91db1e1571f5e267df0e8c017ba33d85db929eb25e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He clearly decided that was a bad idea. But, and some of those lessons, you might call them transferable skills, which perhaps goes back to the heart of what we're talking about, are important, I think. And one of them is not kidding yourself, not deluding yourself into thinking that you understand something, not not. So it's a really understanding that the world is very complicated. And there are many ways of,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 414, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a828abf5-0438-4f0d-84c2-b2ea383c70b9": {"__data__": {"id_": "a828abf5-0438-4f0d-84c2-b2ea383c70b9", "embedding": null, "metadata": {"start_time": 239.46, "end_time": 264.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0b070a7a-a4c0-4554-a7fd-b7102d705b65", "node_type": "4", "metadata": {"start_time": 239.46, "end_time": 264.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "0ba869863c7fa0ab2c4700b6c21515060d76138020045c940ded89d894b2cb69", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "It's difficult to understand a black hole, right? A collapsing star. But it's also difficult to understand how to run a society. It's really tricky. So there aren't simple answers. And from we could talk, I'll stop talking about that, but I could talk about it forever. But that I think is always when I got the opportunity to talk about science in my mind,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 357, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9d30c286-c7d5-42be-a0b9-dc3cd2f324cf": {"__data__": {"id_": "9d30c286-c7d5-42be-a0b9-dc3cd2f324cf", "embedding": null, "metadata": {"start_time": 264.78, "end_time": 291.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9fd09855-435a-44d5-a61d-61d5662819a6", "node_type": "4", "metadata": {"start_time": 264.78, "end_time": 291.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "90f7981e9f182ca1e53987d4970f08f5422528a486b76d93791c0d202bd33739", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "that I enjoy talking about it, I think it's wonderful, I get excited about talking about these big ideas. But also in my mind there was also the things that <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and other of my heroes had also said, which is that there should be, there is a responsibility to talk about this way of thinking and the things we've discovered. Which leads us into one of the areas that I think you're masterful at if you don't mind me saying,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 457, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "aa4504c0-019c-4cde-8801-d74970a09b9a": {"__data__": {"id_": "aa4504c0-019c-4cde-8801-d74970a09b9a", "embedding": null, "metadata": {"start_time": 291.86, "end_time": 314.34, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0f734f30-9135-4eae-b5c8-3d9b74c3b2ff", "node_type": "4", "metadata": {"start_time": 291.86, "end_time": 314.34, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "ad220ea7e13003651e67e50a40eb060390290008aafe6c0ce7e367755e2d8f16", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "is overcoming a common trait that you see with lots of intelligent people, the curse of knowledge, that you know an awful lot but your ability to translate that knowledge and make it accessible to seven-year-old children or to a mass audience on the BBC is a unique skill set in itself and I'm interested", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 304, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "765bdb71-91cb-4359-af7e-dae352e74a30": {"__data__": {"id_": "765bdb71-91cb-4359-af7e-dae352e74a30", "embedding": null, "metadata": {"start_time": 314.6, "end_time": 332.06, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8ca51b17-ece2-4bf4-ac37-c36ee91ea04c", "node_type": "4", "metadata": {"start_time": 314.6, "end_time": 332.06, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "b0e03a034ecd78eec7b4cb691e9711f2acf4a788a9c5f1698306044accf4918f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "and exploring how do you go about being able to communicate these complex difficult ideas in a way that people can understand and start to get to grips with. You know in part it's about that we talked earlier about honesty, being honest with yourself.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 251, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9b4b71fa-d963-44d8-9587-8a87402fbe84": {"__data__": {"id_": "9b4b71fa-d963-44d8-9587-8a87402fbe84", "embedding": null, "metadata": {"start_time": 332.5, "end_time": 360.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f059ae34-2f2d-4157-ad3f-8f33373183e1", "node_type": "4", "metadata": {"start_time": 332.5, "end_time": 360.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "22b0a3804e8dd55b19204e89c57128f2ceb46d953837067a67b288ed84fca199", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "about how difficult it is to understand some of these concepts. So if you've been through the process and I find this, I'm quite slow quite often. I just, I don't understand that, I understand that, I understand. Oh yeah, that's it. Then what I do usually is just talk about the way that I understand something. And it's quite often the way that a seven year old would understand something because you've been, I find anyway, if you're honest with yourself then", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 461, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1597899a-c542-48e9-9dc3-e183aa5172d2": {"__data__": {"id_": "1597899a-c542-48e9-9dc3-e183aa5172d2", "embedding": null, "metadata": {"start_time": 360.14, "end_time": 366.98, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "586c9e35-a1cc-4310-a90d-c38e4c541bf1", "node_type": "4", "metadata": {"start_time": 360.14, "end_time": 366.98, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "9e56d6360c1c491ff5ee831acee4d6ab419d4f12e92dde1e5a8a38548b7014d8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "What if you really do understand if you really deeply get something you've been through that process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 101, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "831b4b65-5f76-4697-9866-8336f1217595": {"__data__": {"id_": "831b4b65-5f76-4697-9866-8336f1217595", "embedding": null, "metadata": {"start_time": 367.2, "end_time": 395.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d629396f-04c1-4e41-b1a9-f981897ef1f4", "node_type": "4", "metadata": {"start_time": 367.2, "end_time": 395.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "d9ef7973887aae12e98145641682049dac7f2f57b5a0ee11de225321784a57ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So you've seen how difficult it is and it's almost always difficult. So you're not going to wave your hands around and obfuscate and say, when you see someone doing that and you see it at university, I saw it with people who taught me, you can tell when they don't really understand something because they fall back on jargon and so wave their hands around and go, no, no, no. And usually that's because they've not been through that process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 442, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3813f612-3fe8-49a8-920b-53e76c2ed13d": {"__data__": {"id_": "3813f612-3fe8-49a8-920b-53e76c2ed13d", "embedding": null, "metadata": {"start_time": 395.72, "end_time": 422.6, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "38b86218-6666-4ee8-a536-1aaf6edea449", "node_type": "4", "metadata": {"start_time": 395.72, "end_time": 422.6, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "68cc4e89408fa93ebb71e095c25de55184bcda47e3caff593b2c163ef1ab1382", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And they're just, they're not tricking you, you know, but it's probably that they're tricking themselves into thinking that they understand. Are you comfortable saying, I don't know? I don't know. Oh, this is the basis of science. It's fundamental. I mean, <PERSON>, we mentioned you know, it's a <PERSON><PERSON><PERSON> Nobel Prize winning physicist also works on a Manhattan Project actually, one of the greats, a great teacher as well. And he called science a satisfactory philosophy of ignorance.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 491, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3ca9c099-c7d3-472b-a2e1-631d44f61857": {"__data__": {"id_": "3ca9c099-c7d3-472b-a2e1-631d44f61857", "embedding": null, "metadata": {"start_time": 423.04, "end_time": 442.26, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f57d1161-1c8b-449c-ae99-e75172ac83cd", "node_type": "4", "metadata": {"start_time": 423.04, "end_time": 442.26, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "a1670d40697779c690a428dcaeed00b9f799049a0fdc5b32057f32c8dbf94942", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And it's a really deep point actually because even that all knowledge starts from you, one, the individual, accepting that they don't know. You start with, I don't know how that works. I don't know why the sky is blue. I don't know why the leaves are green. I don't know why the universe is the way that it is.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 310, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "81d554cf-dd4e-4314-b575-453b1e75e4e9": {"__data__": {"id_": "81d554cf-dd4e-4314-b575-453b1e75e4e9", "embedding": null, "metadata": {"start_time": 442.64, "end_time": 471.1, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "da0e8d8f-a4eb-45aa-a8ed-4fc00a6a56d8", "node_type": "4", "metadata": {"start_time": 442.64, "end_time": 471.1, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "652e504e2c876308e23c9a83b16dd54f0623c755426c532117866aceba216047", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And that's the you have to start from that point and then you build you try to build a reliable picture. It's a model of the world in your mind. But being a scientist, of course, it's about doing research. And that means that you're going to the edge of knowledge always and being extremely comfortable standing on the edge of the known, the dividing line between the known and the unknown and trying to find out a bit more. So you have to be delighted to not know.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 465, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "fce4c0b4-66b1-4fe9-92c7-8c287c02879c": {"__data__": {"id_": "fce4c0b4-66b1-4fe9-92c7-8c287c02879c", "embedding": null, "metadata": {"start_time": 471.32, "end_time": 497.38, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9e404a5f-49e5-49e2-a63b-4c88f79acc40", "node_type": "4", "metadata": {"start_time": 471.32, "end_time": 497.38, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "24e380aab4603c61a6258a7ed40f0d2c7646acb35ab6b2721a07e76ba20643e6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "That's an excited about it to go back to what you said, passionate about not knowing in order to make any progress. And so I think that's a skill. It's about it's about jettisoning any fear of the unknown. Isn't it? And I think a lot of times we get into a lot of arguments as a society about things that are pretty unknowable, well unknowable at the moment. We don't know. So even basic things like did the universe have a beginning?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 434, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f978ddfd-8cca-45b5-a579-505959308672": {"__data__": {"id_": "f978ddfd-8cca-45b5-a579-505959308672", "embedding": null, "metadata": {"start_time": 497.84, "end_time": 515.22, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1d205454-5011-4033-aeea-52c2cfc6ad25", "node_type": "4", "metadata": {"start_time": 497.84, "end_time": 515.22, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "6a617750885f1e8eb937b19d5c8283d8d62142dc28348d1e2b6c62162dc08336", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Right and we know that the universe is very hot and dense 13.8 billion years ago with me that's good we call that the big bang but whether that was a beginning in time whether the universe existed in some form before that what it means to talk about the beginning of time we don't even know what time is.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 304, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "35e82b25-0571-4d15-9ad2-450170ce2963": {"__data__": {"id_": "35e82b25-0571-4d15-9ad2-450170ce2963", "embedding": null, "metadata": {"start_time": 515.44, "end_time": 537.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "dfcb403f-b5e4-4e78-a110-23f7c23674c3", "node_type": "4", "metadata": {"start_time": 515.44, "end_time": 537.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "1979adbd4a59205c4f0bc4bfafcc4021e5bd4cd0aff0a5ce84b6060b4114f51c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Right, we've got some sense by the way that it might be built of smaller things but we can leave that alone. But the thing is, it's key, isn't it? Because a lot of people talk with great confidence about, well, how I know how the universe began, I know why the universe began. The answer is, how can you... Nobody knows, we don't know yet.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 339, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "64b07a1e-496c-48e8-b06a-54a3d4409476": {"__data__": {"id_": "64b07a1e-496c-48e8-b06a-54a3d4409476", "embedding": null, "metadata": {"start_time": 537.52, "end_time": 546.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9ea26817-f414-45dd-bc5e-ed6330341c66", "node_type": "4", "metadata": {"start_time": 537.52, "end_time": 546.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "aad00aff3dfb2c2d409affb12a1661374d02b345f7d17e771f15135123467536", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And that's interesting and exciting. And that's the key, that's the starting point for building a reliable... But we live in a society where we...", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 146, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "95969d7c-5726-45b3-b95b-2b5972bc9cee": {"__data__": {"id_": "95969d7c-5726-45b3-b95b-2b5972bc9cee", "embedding": null, "metadata": {"start_time": 546.94, "end_time": 572.84, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ee561a68-171d-4db9-ba73-397cb3b67f1b", "node_type": "4", "metadata": {"start_time": 546.94, "end_time": 572.84, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "a45ac9b85e9a4f7af68cd2dcabaee2e16e7f1789b0cb4ac533e143b95bb65ffd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Pillary people for not knowing. We demote people for saying I don't understand. We criticise people for not having all the answers. We promote people who bluff and bluster and trick their way to the top. We can say apolitical, but yeah. Whether that's political or even a business. The sort of people that are leading businesses are just those that are brave enough to say, I know all the answers, even if they don't.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 417, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5f52086f-1d5d-4646-8509-25c125f4fdbb": {"__data__": {"id_": "5f52086f-1d5d-4646-8509-25c125f4fdbb", "embedding": null, "metadata": {"start_time": 573.06, "end_time": 601.88, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b4c888a3-5542-41b4-abbe-36138ca53be5", "node_type": "4", "metadata": {"start_time": 573.06, "end_time": 601.88, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "743c8a2087861b224fe1dbaac243f06e615b07b4ada2db0a9c0d9318f59c42c1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "We're all making it up. And I think it's really powerful if we, if you can sit here and say, I don't know, then that's great for everyone. Yeah. And if you think about it, so we do accept in certain professions that it's good to be, to know what you're doing. Like for example, flying a plane. Yeah. Being an airline captain. Doing an operation. The surgeon, the designer of a nuclear reactor, you know, there are things where knowledge and experience are valued.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 463, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "073a1e7f-4d2d-4a2c-8cac-8e9faca24106": {"__data__": {"id_": "073a1e7f-4d2d-4a2c-8cac-8e9faca24106", "embedding": null, "metadata": {"start_time": 602.12, "end_time": 629.9, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2467e199-f6b1-4c06-bdf4-7fec0e28779d", "node_type": "4", "metadata": {"start_time": 602.12, "end_time": 629.9, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "fe85c20ac2b6e5c66ac057ca7917a1689c3ed18b4b35c1d9dce0ee35a73f1931", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "But you're right. I suppose it's if you look at how our knowledge, and I used the term before, reliable knowledge, how that knowledge was acquired, it was acquired by people starting from the basis they didn't understand. And then the moment, this is critical in science, the moment that some new data appears, some new evidence, some new observation,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 351, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "570e4acb-e820-4fbe-b763-534788fbc274": {"__data__": {"id_": "570e4acb-e820-4fbe-b763-534788fbc274", "embedding": null, "metadata": {"start_time": 630.56, "end_time": 655.04, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "39fe741f-1a41-4544-9113-7b4542313e1a", "node_type": "4", "metadata": {"start_time": 630.56, "end_time": 655.04, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "cce921104d576a51d854a3b564c50da0177265aefc513629894c46b95ed777b8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "comes in that conflicts with your picture of the world then you start to be interested you start to be excited you go well if i'm wrong with my picture. Then i'm delighted because i've learned something i can rule that picture out and then i can go on to a new picture so you have to be delighted when you're shown to be wrong because you've learned something.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 360, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}