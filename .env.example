# Environment Variables for HPE Video Intelligence Platform

# GROQ API Configuration
# Get your API key from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# Docker Hub Configuration (for production deployment)
# Replace with your Docker Hub username
DOCKER_USERNAME=your-dockerhub-username

# Optional: Custom model configuration
# GROQ_MODEL=llama-3.1-8b-instant

# Optional: Backend URLs (usually don't need to change these)
# BACKEND_EMBED_URL=http://faiss_backend:8001/embed
# FAISS_BACKEND_URL=http://faiss_backend:8001

# Instructions:
# 1. Copy this file to .env: cp .env.example .env
# 2. Replace 'your_groq_api_key_here' with your actual GROQ API key
# 3. Replace 'your-dockerhub-username' with your Docker Hub username
# 4. For local development: docker-compose -f docker-compose.cpu.yml up -d
# 5. For production: docker-compose -f docker-compose.prod.yml up -d
