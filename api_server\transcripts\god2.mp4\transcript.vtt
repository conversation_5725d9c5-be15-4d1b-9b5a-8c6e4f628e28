WEBVTT

00:00.240 --> 00:17.920
Next let us see how to write an algorithm. Here already I have written an algorithm for swapping two numbers or two elements. See I have used C language like syntax only. Only the thing I have done is here I wrote algorithm to show that okay I am writing an algorithm it's not a program.

00:18.360 --> 00:47.020
Here if we compare with the C program, here I have not written the data type of parameters. Yes, data types we don't decide at algorithm time. When we write a program then we decide what data types are required. And here even I don't have a temporary variable declaration. Declaration is not there, directly it is used. It's understood unless you declare in the program you cannot use it. But in algorithm we don't bother about those minor things that are related to language.

00:49.100 --> 01:29.540
This is more like C. Now if you want to write in different way, you can say this as begin, this as end. In stock lower back edge you can also say begin and end. Next, you can use this symbol also for assignment. Actually this is an assignment symbol that was used in some more languages like Pascal language. So you can use that one. Or else you can use this symbol

01:30.660 --> 01:58.940
For showing that this value is stored at this one. So now it's up to you how you want to write it. I hope whatever I wrote you have understood it. So same way whatever you write I should be able to understand at least you should be able to understand it. Or the people who are involved in the project should be able to understand it. Who are using it should be able to understand it. Right? So you can use any syntax there is no fixed syntax for writing any allegorata.

01:59.360 --> 02:26.220
Let's see how to analyze an algorithm, what is the criteria on which we analyze an algorithm. So the criteria is, the first criteria is, see the algorithms are the procedures for solving problems, whether you do it manually using pen and paper or you make a program and let your machines do it. Whatever the method may be,

02:26.520 --> 02:57.020
How much time it is taking? If the procedure is very lengthy and time consuming or whether the procedure is very fast and quickly you can get the results. So that is one of the important criteria on which we have to analyze. If you are devising a procedure or an algorithm then it should be time efficient means it must be faster. So after writing the algorithms we analyze how much time it is taking. So that time what we get is in the form of a function.

02:57.380 --> 03:29.200
We get a function, time function. We not get the watch time already I have shown you. You will get the function, time function. Now next is space. As the algorithm is going to be converted into a program and it's going to run on the machine then we need to know how much memory space it will consume. So that is the second criteria on which we will analyze in the quarter.

03:29.420 --> 03:55.200
So these are the two major criteria on which the algorithms are analyzed. And furthermore if you have any other criteria or criteria and furthermore if you have any other criteria or any other factors you want to consider you can consider them like. Now

03:56.080 --> 04:10.960
Nowadays every application is either internet based or web based or say cloud based. So data transfer or network consumption is also an important criteria. How much data is going to be transferred?

04:11.580 --> 04:45.860
See if the algorithm or means if a procedure that you are writing if it is not necessary transferring larger size data or it can be reduced or compressed whatever it is. So that is one more important criteria that is how much data transfer is done. Nowadays most of the devices are handled instead of PCs we are using palm tops and laptops and tablets then power consumption is also a criteria. How much power it is consuming?

04:48.500 --> 05:11.080
And one more criteria is if you are developing an algorithm for device drivers or system level programming, if you are developing something, some algorithm if you are writing, then you may also need to know how much CPU registers it is consuming.

05:11.480 --> 05:40.580
So CPU will also have some memory called as registers. How many registers it is consuming? That's also one of the worrying factor when you are designing an Elgort or writing a program for it. So these are few criteria I have listed and it depends on your requirement, it depends on your project, right? What are the more criteria you need to analyze? Now let us see, I have taken one example to show you how we analyze an Elgort.

05:40.820 --> 06:05.840
So for an answer purpose we need to know the time. So first we do time analysis then we do space analysis. Now time means how much time this will take. This is not the wash time then how to assume. Now we say that every statement in an algorithm takes one unit of time. Every simple statement in an algorithm takes one unit of time.

06:06.040 --> 06:34.760
If suppose the algorithm is calling another algorithm, it's using another procedure then you have to analyze that also in detail. So simple means just statements, direct statements, it's not a nested statement. So single statement each statement takes one unit of time. So here I assume it takes one unit of time, one unit of time, one unit. So problem time is three. So the time function f of n is three. Here we got the constant value

06:35.020 --> 07:06.360
because I have taken a very simple algorithm and the answer is 3 that is some constant value right 3 or 30 whatever it is it is constant value fixed value we got so there's a time function it is not in terms of n just we got a constant value. Now here whatever the complexity in the statement may be how lengthy the statement will be we say that it takes volume of time let us assume instead of this statement I have some statement saying

07:06.840 --> 07:31.500
x assigned 5 into plus 6 into if this is the statement written in an algorithm. Then for this also we say it takes 1 unit of time but really if you see when you convert into a program and finally when it gets converted into machine code then how many statements it will have

07:31.880 --> 08:01.000
for each multiplication, two statements, then for addition one statement, then assignment one statement. So total four statements will be there but we don't want to go into that much detail, we don't want to see how it's going to get converted to machine code but simply we analyze and say each statement takes one year of time. Now suppose if you want to go into detail analysis like this, you can go into detail.

08:01.680 --> 08:29.260
You can imagine up to the level of the machine code that's going to be generated. It is just like, suppose you want to travel to your friend's house in a car. You don't have to think and you don't have to plan and analyze. Simply you can take a car or a bike or a vehicle and you can just start and reach his place. Right? That's simple. If you want to land on Mars,

08:30.740 --> 08:50.800
Simply you cannot take a satellite so you cannot take a rocket and launch it and reach there. It's not easy. So you have to analyze and design and make a complete plan so that your mission is successful. So that's it. Here you need to go into each and every minor detail you have to take.

08:52.000 --> 09:16.800
So it depends on your requirement. So if you want to analyze into greater detail then do it. But something point of view we will be doing it at basic level right. At a very shallow level this brief analysis we will do will not go into much detail but much detail analysis can also be done. Next is space analysis.

09:17.920 --> 09:44.760
What are the variables used here? For a space I will write down the variables a is one parameter b is also a parameter and temp is a local variable used. So to tell how many variables 1, 2 and 3. So there are three variables used. So the space is constant again. It is just 3. I got the answer as 3. So again it is also constant.

09:45.700 --> 10:17.420
So usually we represent them as order of one, constant is order of one and that's also order of one means it is constant one represents constant if it is three also we write one if it is three thousand also we write one saying that it is constant value constant. So this how each statement is taken as one unit of time and each variable is taken as one word so these are

10:20.140 --> 10:36.160
3 words. Why we are saying words there? Not bytes. We don't know. When we converted to a program, it may be an integer type or float type or double type that we don't know. So that's why we say it is taking words.

