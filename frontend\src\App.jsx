import React, { useState } from 'react';
import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import { CloudUploadIcon, MessageSquareIcon, FileTextIcon, LogOutIcon } from 'lucide-react';

// Import components
import FileUpload from './components/FileUpload';
import ChatInterface from './components/ChatInterface';
import TranscriptList from './components/TranscriptList';
import TranscriptDetail from './components/TranscriptDetail';

const Sidebar = ({ activeRoute, onRouteChange }) => {
  return (
    <div className="w-20 bg-white border-r border-gray-200 flex flex-col items-center py-6 space-y-4">
      <button 
        onClick={() => onRouteChange('/upload')}
        className={`
          p-3 rounded-lg transition-colors 
          ${activeRoute === '/upload' 
            ? 'bg-indigo-50 text-indigo-600' 
            : 'text-gray-500 hover:bg-gray-50'}
        `}
      >
        <CloudUploadIcon className="w-6 h-6" />
      </button>
      <button 
        onClick={() => onRouteChange('/chat')}
        className={`
          p-3 rounded-lg transition-colors 
          ${activeRoute === '/chat' 
            ? 'bg-indigo-50 text-indigo-600' 
            : 'text-gray-500 hover:bg-gray-50'}
        `}
      >
        <MessageSquareIcon className="w-6 h-6" />
      </button>
      <button 
        onClick={() => onRouteChange('/transcripts')}
        className={`
          p-3 rounded-lg transition-colors 
          ${activeRoute.startsWith('/transcript') 
            ? 'bg-indigo-50 text-indigo-600' 
            : 'text-gray-500 hover:bg-gray-50'}
        `}
      >
        <FileTextIcon className="w-6 h-6" />
      </button>
    </div>
  );
};

const App = () => {
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [activeRoute, setActiveRoute] = useState(window.location.pathname || '/upload');
  const [sliderValue, setSliderValue] = useState(29); // Initial value centered at 29

  const handleUploadComplete = (files) => {
    setUploadedFiles(files);
    // If the upload also includes a transcript, redirect to the transcript detail page
    if (files.length === 1 && files[0].id) {
      window.location.href = `/transcripts/${files[0].id}`;
    } else {
      // Otherwise redirect to chat
      window.location.href = '/chat';
    }
  };

  const handleRouteChange = (route) => {
    setActiveRoute(route);
    window.location.href = route;
  };

  const handleLogout = () => {
    // Implement logout logic
    console.log('Logging out...');
    // Redirect to login or home page
    window.location.href = '/';
  };

  const handleSliderChange = (e) => {
    setSliderValue(parseInt(e.target.value));
  };

  return (
    <Router>
      <div className="flex h-screen bg-gray-100">
        {/* Sidebar */}
        <Sidebar 
          activeRoute={activeRoute} 
          onRouteChange={handleRouteChange} 
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Top Navigation */}
          <div className="bg-white border-b border-gray-200 p-4 flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">
              Video Intelligence Platform
            </h1>
            <button 
              onClick={handleLogout}
              className="text-gray-500 hover:bg-gray-100 p-2 rounded-full"
            >
              <LogOutIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Routes */}
          <div className="flex-1 overflow-auto">
            <Routes>
              {/* Upload Page */}
              <Route
                path="/upload"
                element={
                  <div className="max-w-xl mx-auto p-6">
                    <h2 className="text-3xl font-bold text-center mb-6 text-gray-800">
                      Upload Your Media
                    </h2>
                    <FileUpload 
                      onUploadComplete={handleUploadComplete}
                      speech_len={sliderValue}
                    />
                    
                    {/* Slider Component using Tailwind */}
                    <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="flex justify-between mb-2 text-sm text-gray-500">
                        <span>10</span>
                        <span className="font-medium text-gray-700">Speech Len (Interval): {sliderValue}</span>
                        <span>50</span>
                      </div>
                      <div className="relative">
                        <input
                          type="range"
                          min="10"
                          max="50"
                          value={sliderValue}
                          onChange={handleSliderChange}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none"
                          style={{
                            // These minimal inline styles are needed as Tailwind doesn't have direct support for styling range inputs
                            accentColor: '#4f46e5', // Indigo color for browsers that support accent-color
                          }}
                        />
                      </div>
                    </div>
                  </div>
                }
              />
              
              {/* Chat Interface */}
              <Route 
                path="/chat" 
                element={
                  uploadedFiles.length >= 0 ? (
                    <ChatInterface 
                      uploadedFiles={uploadedFiles} 
                    />
                  ) : (
                    <Navigate to="/upload" replace />
                  )
                } 
              />

              {/* Transcripts List */}
              <Route 
                path="/transcripts" 
                element={<TranscriptList />} 
              />

              {/* Transcript Detail */}
              <Route 
                path="/transcripts/:id" 
                element={<TranscriptDetail />} 
              />

              {/* Default Redirect */}
              <Route 
                path="*" 
                element={<Navigate to="/upload" replace />} 
              />
            </Routes>
          </div>
        </div>
      </div>
    </Router>
  );
};

export default App;