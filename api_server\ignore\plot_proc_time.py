import json
import matplotlib.pyplot as plt
import numpy as np

# Structured data - now with speech_len_values in ascending order
data = {
    "speech_len_values": [10, 29, 50],  # Changed the order to ascending
    "files": ["brian.mp3", "cillian.mp3", "test.mp3"],
    "measurements": {
        "29": {
            "brian.mp3": {"avg_interval": 22.5, "processing_time": 7.7},
            "cillian.mp3": {"avg_interval": 27.75, "processing_time": 6.9},
            "test.mp3": {"avg_interval": 21.3, "processing_time": 0.9}
        },
        "10": {
            "brian.mp3": {"avg_interval": 7.0, "processing_time": 16.14},
            "cillian.mp3": {"avg_interval": 8.7, "processing_time": 15.5},
            "test.mp3": {"avg_interval": 9.14, "processing_time": 2.1}
        },
        "50": {
            "brian.mp3": {"avg_interval": 36.3, "processing_time": 8.1},
            "cillian.mp3": {"avg_interval": 50.3, "processing_time": 8.3},
            "test.mp3": {"avg_interval": 32.3, "processing_time": 1.8}
        }
    }
}

# Save to JSON file
with open('speech_data.json', 'w') as f:
    json.dump(data, f, indent=4)

# Create graphs
speech_len_values = data["speech_len_values"]  # Now in proper order: [10, 29, 50]
file_names = data["files"]
markers = ['o', 's', '^']  # Different markers for different files
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']  # Different colors for different files

# Figure 1: Average Time Interval vs Speech Length
plt.figure(figsize=(12, 6))
for i, file_name in enumerate(file_names):
    avg_intervals = [data["measurements"][str(sl)][file_name]["avg_interval"] for sl in speech_len_values]
    plt.plot(speech_len_values, avg_intervals, marker=markers[i], color=colors[i], label=file_name, linewidth=2, markersize=8)

plt.xlabel('Speech Length Value', fontsize=12)
plt.ylabel('Average Time Interval (seconds)', fontsize=12)
plt.title('Average Time Interval vs Speech Length Value', fontsize=14)
plt.grid(True, linestyle='--', alpha=0.7)
plt.legend(fontsize=10)
plt.tight_layout()
plt.savefig('avg_interval_vs_speech_len.png')

# Figure 2: Processing Time vs Speech Length
plt.figure(figsize=(12, 6))
for i, file_name in enumerate(file_names):
    processing_times = [data["measurements"][str(sl)][file_name]["processing_time"] for sl in speech_len_values]
    plt.plot(speech_len_values, processing_times, marker=markers[i], color=colors[i], label=file_name, linewidth=2, markersize=8)

plt.xlabel('Speech Length Value', fontsize=12)
plt.ylabel('Processing Time (seconds)', fontsize=12)
plt.title('Processing Time vs Speech Length Value', fontsize=14)
plt.grid(True, linestyle='--', alpha=0.7)
plt.legend(fontsize=10)
plt.tight_layout()
plt.savefig('processing_time_vs_speech_len.png')

# Figure 3: Bar chart comparing both metrics for each file at different speech lengths
width = 0.35
x = np.arange(len(file_names))

fig, axes = plt.subplots(1, 3, figsize=(18, 6))
speech_len_labels = ["Minimum (10)", "Default (29)", "Maximum (50)"]  # Updated order to match values

for i, sl in enumerate(speech_len_values):
    avg_intervals = [data["measurements"][str(sl)][file]["avg_interval"] for file in file_names]
    proc_times = [data["measurements"][str(sl)][file]["processing_time"] for file in file_names]
    
    axes[i].bar(x - width/2, avg_intervals, width, label='Avg Interval (s)', color='#1f77b4')
    axes[i].bar(x + width/2, proc_times, width, label='Processing Time (s)', color='#ff7f0e')
    
    axes[i].set_title(f'Speech Length: {speech_len_labels[i]}')
    axes[i].set_xticks(x)
    axes[i].set_xticklabels(file_names)
    axes[i].grid(True, linestyle='--', alpha=0.3, axis='y')
    axes[i].legend()

plt.tight_layout()
plt.savefig('comparison_by_speech_len.png')

print("JSON file and graphs have been created!")