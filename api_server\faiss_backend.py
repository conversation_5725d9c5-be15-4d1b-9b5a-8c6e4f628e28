from fastapi import FastAPI, HTTPException
import json
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from collections import defaultdict
import pickle
import math
import shutil

from pydantic import BaseModel
from groq import Groq
import os
from typing import List

def format_timestamp(seconds):
    """Format seconds into MM:SS.mmm format for display"""
    if seconds is None:
        return "00:00.000"

    minutes = math.floor(seconds / 60)
    remaining_seconds = seconds % 60
    milliseconds = int((remaining_seconds - math.floor(remaining_seconds)) * 1000)

    return f"{minutes:02d}:{math.floor(remaining_seconds):02d}.{milliseconds:03d}"

app = FastAPI()
model = SentenceTransformer("all-MiniLM-L6-v2")  # Lightweight embedding model

# Create directories for storing indexes if they don't exist
index_dir = "embeddings"
merged_dir = "merged_embeddings"
os.makedirs(index_dir, exist_ok=True)
os.makedirs(merged_dir, exist_ok=True)

# Create a directory for chat-to-videos mapping
chat_mapping_dir = "chat_mappings"
os.makedirs(chat_mapping_dir, exist_ok=True)
chat_mapping_file = os.path.join(chat_mapping_dir, "chat_to_videos.json")

# Initialize chat-to-videos mapping
if os.path.exists(chat_mapping_file):
    with open(chat_mapping_file, 'r') as f:
        try:
            chat_to_videos = json.load(f)
        except json.JSONDecodeError:
            chat_to_videos = {}
else:
    chat_to_videos = {}

# We will not store indexes in memory, but always load from disk

# Embedding dimension
dim = 384  # Embedding size of "all-MiniLM-L6-v2"

class FilePathRequest(BaseModel):
    file_path: str
    context_id: str = "default"

class ChatRequest(BaseModel):
    query: str
    top_k: int = 3  # Default to retrieving top 3 most similar texts
    model: str = "llama-3.1-8b-instant"  # Default model
    context_id: str = "default"  # Context ID to use for retrieval

class MergeRequest(BaseModel):
    chat_id: str
    video_ids: List[str]

@app.post("/embed")
async def embed_texts(request: FilePathRequest):
    file_path = request.file_path
    context_id = request.context_id

    if not os.path.exists(file_path):
        raise HTTPException(status_code=400, detail="File not found")

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            json_data = json.load(f)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")

    # Process transcript chunks with timestamp metadata
    new_texts = []
    new_metadata = []

    for item in json_data:
        # Extract text and timestamp metadata
        text = item.get("text") or item.get("content") or item.get("description")
        start_time = item.get("start_time")
        end_time = item.get("end_time")

        if text and start_time is not None and end_time is not None:
            new_texts.append(text)
            new_metadata.append({
                "start_time": start_time,
                "end_time": end_time,
                "text": text
            })
        elif text:
            # Fallback for non-transcript data
            new_texts.append(text)
            new_metadata.append({
                "text": text
            })

    if not new_texts:
        raise HTTPException(status_code=400, detail="No valid texts found in JSON")

    # File paths for index, texts, and metadata
    index_file = os.path.join(index_dir, f"{context_id}.index")
    texts_file = os.path.join(index_dir, f"{context_id}.pkl")
    metadata_file = os.path.join(index_dir, f"{context_id}_metadata.pkl")

    # Check if embeddings already exist for this context_id
    existing_texts = []
    existing_metadata = []
    existing_index = None

    if os.path.exists(index_file) and os.path.exists(texts_file):
        # Load existing index and texts
        try:
            existing_index = faiss.read_index(index_file)
            with open(texts_file, "rb") as f:
                existing_texts = pickle.load(f)

            # Load metadata if it exists
            if os.path.exists(metadata_file):
                with open(metadata_file, "rb") as f:
                    existing_metadata = pickle.load(f)
            else:
                # Create empty metadata for existing texts if metadata file doesn't exist
                existing_metadata = [{"text": text} for text in existing_texts]

            print(f"Found existing embeddings for context '{context_id}' with {existing_index.ntotal} vectors and {len(existing_texts)} texts")
        except Exception as e:
            print(f"Error loading existing embeddings for context '{context_id}': {str(e)}")
            # If there's an error loading existing data, we'll create a new index
            existing_index = None
            existing_texts = []
            existing_metadata = []

    # Combine existing texts and metadata with new ones
    all_texts = existing_texts + new_texts
    all_metadata = existing_metadata + new_metadata

    # Convert new texts to vector embeddings
    new_embeddings = model.encode(new_texts)

    if existing_index is not None:
        # If we have an existing index, add the new embeddings to it
        existing_index.add(np.array(new_embeddings, dtype=np.float32))
        index = existing_index
        print(f"Added {len(new_texts)} new embeddings to existing index for context '{context_id}'")
    else:
        # Otherwise, create a new index with all texts
        index = faiss.IndexFlatL2(dim)
        new_embeddings = model.encode(all_texts)  # Re-encode all texts for consistency
        index.add(np.array(new_embeddings, dtype=np.float32))
        print(f"Created new index for context '{context_id}' with {len(all_texts)} embeddings")

    # Save updated index, texts, and metadata to files
    faiss.write_index(index, index_file)

    with open(texts_file, "wb") as f:
        pickle.dump(all_texts, f)

    with open(metadata_file, "wb") as f:
        pickle.dump(all_metadata, f)

    return {
        "message": f"Stored {len(new_texts)} new embeddings with timestamp metadata for context '{context_id}', total: {len(all_texts)}",
        "context_id": context_id,
        "total_texts": len(all_texts),
        "new_texts": len(new_texts),
        "existing_texts": len(existing_texts)
    }

@app.get("/available_embeddings")
async def get_available_embeddings():
    """Get a list of all available video embeddings."""
    try:
        # Get all files in the embeddings directory
        all_files = os.listdir(index_dir)

        # Extract base names without extensions and filter out duplicates
        embedding_names = set()
        for filename in all_files:
            # Skip any system files, hidden files, or metadata files
            if filename.startswith('.') or filename.startswith('__') or filename.endswith('_metadata.pkl'):
                continue

            # Only include .index files
            if filename.endswith('.index'):
                base_name = os.path.splitext(filename)[0]
                if base_name:
                    # We're now using base filenames without timestamps
                    embedding_names.add(base_name)

        # Convert to sorted list
        sorted_names = sorted(list(embedding_names))

        return {"embeddings": sorted_names}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving available embeddings: {str(e)}"
        )

@app.post("/merge_embeddings")
async def merge_embeddings(request: MergeRequest):
    """Merge multiple embedding files into a single index for a chat."""
    chat_id = request.chat_id
    video_ids = request.video_ids

    if not video_ids:
        raise HTTPException(
            status_code=400,
            detail="No video IDs provided for merging"
        )

    # Check if merged index already exists
    merged_index_file = os.path.join(merged_dir, f"{chat_id}.index")
    merged_texts_file = os.path.join(merged_dir, f"{chat_id}.pkl")
    merged_metadata_file = os.path.join(merged_dir, f"{chat_id}_metadata.pkl")

    # Update the chat-to-videos mapping
    chat_to_videos[chat_id] = video_ids
    with open(chat_mapping_file, 'w') as f:
        json.dump(chat_to_videos, f, indent=2)

    # If merged index already exists, return success
    if os.path.exists(merged_index_file) and os.path.exists(merged_texts_file) and os.path.exists(merged_metadata_file):
        return {
            "message": f"Merged index for chat '{chat_id}' already exists",
            "chat_id": chat_id,
            "video_ids": video_ids
        }

    # Collect all texts and metadata from the specified video IDs
    all_texts = []
    all_metadata = []

    for video_id in video_ids:
        index_file = os.path.join(index_dir, f"{video_id}.index")
        texts_file = os.path.join(index_dir, f"{video_id}.pkl")
        metadata_file = os.path.join(index_dir, f"{video_id}_metadata.pkl")

        if not os.path.exists(index_file) or not os.path.exists(texts_file):
            raise HTTPException(
                status_code=404,
                detail=f"Embedding for video '{video_id}' not found"
            )

        # Load texts
        with open(texts_file, "rb") as f:
            texts = pickle.load(f)
            all_texts.extend(texts)

        # Load metadata if it exists
        if os.path.exists(metadata_file):
            with open(metadata_file, "rb") as f:
                metadata = pickle.load(f)
                # Add video_id to each metadata entry for tracking
                for meta in metadata:
                    meta["video_id"] = video_id
                all_metadata.extend(metadata)
        else:
            # Create basic metadata if file doesn't exist
            all_metadata.extend([{"text": text, "video_id": video_id} for text in texts])

    # Create a new index with all texts
    merged_index = faiss.IndexFlatL2(dim)
    merged_embeddings = model.encode(all_texts)
    merged_index.add(np.array(merged_embeddings, dtype=np.float32))

    # Save merged index, texts, and metadata
    faiss.write_index(merged_index, merged_index_file)

    with open(merged_texts_file, "wb") as f:
        pickle.dump(all_texts, f)

    with open(merged_metadata_file, "wb") as f:
        pickle.dump(all_metadata, f)

    return {
        "message": f"Successfully merged {len(video_ids)} videos into index for chat '{chat_id}'",
        "chat_id": chat_id,
        "video_ids": video_ids,
        "total_texts": len(all_texts)
    }

@app.post("/chat")
async def chat_with_context(request: ChatRequest):
    context_id = request.context_id

    # Special handling for GENERAL context
    if context_id == "GENERAL":
        # Skip context retrieval for GENERAL mode
        retrieved_texts = []
        cleaned_contexts = []
        retrieved_metadata = []
    else:
        # Check if this is a chat ID with merged embeddings
        if context_id in chat_to_videos:
            # Use merged index
            index_file = os.path.join(merged_dir, f"{context_id}.index")
            texts_file = os.path.join(merged_dir, f"{context_id}.pkl")
            metadata_file = os.path.join(merged_dir, f"{context_id}_metadata.pkl")
        else:
            # Use regular index
            index_file = os.path.join(index_dir, f"{context_id}.index")
            texts_file = os.path.join(index_dir, f"{context_id}.pkl")
            metadata_file = os.path.join(index_dir, f"{context_id}_metadata.pkl")

        # Try to load the index, texts, and metadata from files
        if os.path.exists(index_file) and os.path.exists(texts_file):
            try:
                index = faiss.read_index(index_file)
                with open(texts_file, "rb") as f:
                    texts = pickle.load(f)

                # Load metadata if it exists
                metadata = []
                if os.path.exists(metadata_file):
                    with open(metadata_file, "rb") as f:
                        metadata = pickle.load(f)
                else:
                    # Create basic metadata if file doesn't exist
                    metadata = [{"text": text} for text in texts]
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error loading index for context '{context_id}': {str(e)}"
                )
        else:
            raise HTTPException(
                status_code=404,
                detail=f"No embeddings found for context '{context_id}'. Use /embed endpoint first."
            )

        # Validate prerequisites
        if not texts:
            raise HTTPException(
                status_code=400,
                detail=f"No texts have been embedded for context '{context_id}'. Use /embed endpoint first."
            )

    try:
        # Only perform context retrieval if not in GENERAL mode
        if context_id != "GENERAL":
            # Embed the query
            query_embedding = model.encode([request.query])

            # Determine safe top_k value
            top_k = min(request.top_k or 3, len(texts))

            # Perform similarity search
            _, I = index.search(
                np.array(query_embedding, dtype=np.float32),
                top_k
            )

            # Retrieve most similar texts and their metadata
            retrieved_indices = [i for i in I[0] if 0 <= i < len(texts)]
            retrieved_texts = [texts[i] for i in retrieved_indices]
            retrieved_metadata = [metadata[i] for i in retrieved_indices] if metadata else []

            # Clean and preprocess context
            cleaned_contexts = [
                ' '.join(text.split())  # Remove extra whitespaces
                for text in retrieved_texts
            ]
        # Note: for GENERAL mode, retrieved_texts, cleaned_contexts, and retrieved_metadata are already initialized as empty lists

        # Construct prompt based on the mode
        if context_id == "GENERAL":
            # For GENERAL mode, use a direct conversation prompt without context
            prompt = f"""Query: {request.query}

Provide a helpful, concise answer to this query."""
        else:
            # For context-aware mode, include the retrieved context with timestamps
            context_parts = []
            for i, text in enumerate(cleaned_contexts):
                if retrieved_metadata and i < len(retrieved_metadata):
                    meta = retrieved_metadata[i]
                    if "start_time" in meta and "end_time" in meta:
                        # Format timestamps as MM:SS.mmm
                        start_formatted = format_timestamp(meta["start_time"])
                        end_formatted = format_timestamp(meta["end_time"])
                        context_parts.append(f"[{start_formatted} - {end_formatted}] {text}")
                    else:
                        context_parts.append(text)
                else:
                    context_parts.append(text)

            context = "\n\n".join(context_parts)
            prompt = f"""Context:
{context}

Query: {request.query}

Provide a clear, concise, and direct answer to the query based strictly on the given context. Focus on extracting the key information and avoid unnecessary elaboration. If the context includes timestamps, you can refer to specific parts of the content by their time markers."""

        # Generate response using Groq API with more precise prompting
        try:
            # Initialize Groq client with API key
            client = Groq(api_key="********************************************************")

            # Create chat completion using Groq API
            # Use the model specified in the request or fall back to default
            groq_model = request.model if request.model else "llama-3.1-8b-instant"

            # Map frontend model names to actual Groq model names if needed
            model_mapping = {
                "llama-3.3-70b-versatile": "llama3-70b-8192",
                "llama-3.1-8b-instant": "llama3-8b-8192",
                "gemma2-9b-it": "gemma2-9b-it",
                "meta-llama/llama-4-maverick-17b-128e-instruct": "llama-4-maverick-17b-128e-instruct",
                "deepseek-r1-distill-llama-70b": "deepseek-r1-distill-llama-70b",
                "qwen-qwq-32b": "qwen-qwq-32b",
                "compound-beta / mini": "compound-beta-mini"
            }

            groq_model_name = model_mapping.get(groq_model, "llama3-8b-8192")
            print(f"Using model: {groq_model_name} (requested: {groq_model})")

            response = client.chat.completions.create(
                model=groq_model_name,
                messages=[
                    {
                        'role': 'system',
                        'content': 'You are a helpful AI assistant. Provide direct and concise answers to the user\'s questions. Be conversational but keep responses brief and to the point.' if context_id == "GENERAL" else 'You are a precise AI assistant. Extract and summarize key information directly from the given context. Do not provide the context alone. Be concise and factual. Don\'t mention about the context to the user, just use it. Keep the length of the response short.'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                temperature=0.3,
                max_tokens=500
            )

            # Extract and clean the response
            cleaned_response = response.choices[0].message.content.strip()

            # Prepare metadata for response
            context_with_metadata = []
            for i, text in enumerate(cleaned_contexts):
                if i < len(retrieved_metadata):
                    context_with_metadata.append({
                        "text": text,
                        "metadata": retrieved_metadata[i]
                    })
                else:
                    context_with_metadata.append({
                        "text": text,
                        "metadata": {}
                    })

            return {
                "response": cleaned_response,
                "retrieved_contexts": cleaned_contexts,
                "context_metadata": context_with_metadata
            }

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error generating response with Groq API: {str(e)}"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing chat request: {str(e)}"
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "faiss_backend"}