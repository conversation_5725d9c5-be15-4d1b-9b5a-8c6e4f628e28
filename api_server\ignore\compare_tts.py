import os
import time
import uuid
from TTS.api import TTS

# Read input text
with open("input.txt", "r") as f:
    input_text = f.read().strip()

# Output directory
audio_dir = "benchmark_output"
os.makedirs(audio_dir, exist_ok=True)

results = {}

def run_vctk_vits():
    model_name = "vctk/vits"
    model_id = "tts_models/en/vctk/vits"
    tts = TTS(model_id).to("cuda")
    filename = f"{model_name.replace('/', '_')}_{uuid.uuid4()}.wav"
    filepath = os.path.join(audio_dir, filename)

    start = time.time()
    tts.tts_to_file(text=input_text, file_path=filepath)
    end = time.time()

    return round(end - start, 2)

def run_ljspeech_tacotron2_ddc():
    model_name = "ljspeech/tacotron2-DDC"
    model_id = "tts_models/en/ljspeech/tacotron2-DDC"
    tts = TTS(model_id).to("cuda")
    filename = f"{model_name.replace('/', '_')}_{uuid.uuid4()}.wav"
    filepath = os.path.join(audio_dir, filename)

    start = time.time()
    tts.tts_to_file(text=input_text, file_path=filepath)
    end = time.time()

    return round(end - start, 2)

def run_your_tts():
    model_name = "your_tts"
    model_id = "tts_models/multilingual/multi-dataset/your_tts"
    tts = TTS(model_id).to("cuda")
    filename = f"{model_name.replace('/', '_')}_{uuid.uuid4()}.wav"
    filepath = os.path.join(audio_dir, filename)

    start = time.time()
    tts.tts_to_file(
        text=input_text,
        file_path=filepath,
        speaker="en_0",
        language="en"
    )
    end = time.time()
    return round(end - start, 2)

def run_vctk_vits_neon():
    model_name = "vctk/vits-neon"
    model_id = "tts_models/en/vctk/vits-neon"
    tts = TTS(model_id).to("cuda")
    filename = f"{model_name.replace('/', '_')}_{uuid.uuid4()}.wav"
    filepath = os.path.join(audio_dir, filename)

    start = time.time()
    tts.tts_to_file(text=input_text, file_path=filepath)
    end = time.time()
    return round(end - start, 2)

# Run benchmarks
benchmark_functions = {
    "vctk/vits": run_vctk_vits,
    "ljspeech/tacotron2-DDC": run_ljspeech_tacotron2_ddc,
    "your_tts": run_your_tts,
    "vctk/vits-neon": run_vctk_vits_neon
    # "tortoise": run_tortoise  <-- removed due to import error
}

for name, func in benchmark_functions.items():
    try:
        print(f"Running benchmark for: {name}")
        latency = func()
        results[name] = latency
        print(f"✅ {name} completed in {latency} seconds")
    except Exception as e:
        print(f"Error with {name}: {e}")
        results[name] = None

# Final summary
print("\nBenchmark Results:")
for model, latency in results.items():
    print(f"{model}: {latency if latency else 'Error'} seconds")
