#!/bin/bash

# HPE Video Intelligence Platform - Deployment Script
# This script helps deploy the application using Docker Hub images

set -e  # Exit on any error

echo "🚀 HPE Video Intelligence Platform Deployment"
echo "=============================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    echo "📝 Please copy .env.example to .env and configure your settings:"
    echo "   cp .env.example .env"
    echo "   # Edit .env with your GROQ_API_KEY and DOCKER_USERNAME"
    exit 1
fi

# Source environment variables
source .env

# Check required environment variables
if [ -z "$GROQ_API_KEY" ] || [ "$GROQ_API_KEY" = "your_groq_api_key_here" ]; then
    echo "❌ GROQ_API_KEY not configured!"
    echo "📝 Please set your GROQ API key in .env file"
    echo "   Get your key from: https://console.groq.com/keys"
    exit 1
fi

if [ -z "$DOCKER_USERNAME" ] || [ "$DOCKER_USERNAME" = "your-dockerhub-username" ]; then
    echo "❌ DOCKER_USERNAME not configured!"
    echo "📝 Please set your Docker Hub username in .env file"
    exit 1
fi

echo "✅ Environment variables configured"
echo "🐳 Docker Username: $DOCKER_USERNAME"
echo "🔑 GROQ API Key: ${GROQ_API_KEY:0:10}..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running!"
    echo "📝 Please start Docker and try again"
    exit 1
fi

echo "✅ Docker is running"

# Pull latest images
echo "📥 Pulling latest images from Docker Hub..."
docker-compose -f docker-compose.prod.yml pull

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Start services
echo "🚀 Starting services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Health checks
echo "🔍 Performing health checks..."

# Check server health
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Server (port 8000) is healthy"
else
    echo "❌ Server (port 8000) health check failed"
fi

# Check FAISS backend health
if curl -f http://localhost:8001/health > /dev/null 2>&1; then
    echo "✅ FAISS Backend (port 8001) is healthy"
else
    echo "❌ FAISS Backend (port 8001) health check failed"
fi

# Check frontend
if curl -f http://localhost/ > /dev/null 2>&1; then
    echo "✅ Frontend (port 80) is accessible"
else
    echo "❌ Frontend (port 80) is not accessible"
fi

echo ""
echo "🎉 Deployment completed!"
echo "📱 Access your application at:"
echo "   Frontend: http://localhost"
echo "   API Server: http://localhost:8000"
echo "   FAISS Backend: http://localhost:8001"
echo ""
echo "📊 To view logs: docker-compose -f docker-compose.prod.yml logs"
echo "🛑 To stop: docker-compose -f docker-compose.prod.yml down"
