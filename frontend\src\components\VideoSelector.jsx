import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { Check, ChevronDown, ChevronUp, X } from 'lucide-react';

const VideoSelector = ({ onVideosSelected }) => {
  const [availableVideos, setAvailableVideos] = useState([]);
  const [selectedVideos, setSelectedVideos] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [chatId, setChatId] = useState('');
  const dropdownRef = useRef(null);

  // Fetch available videos on component mount
  useEffect(() => {
    fetchAvailableVideos();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch available videos from the backend
  const fetchAvailableVideos = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('http://localhost:8000/available_videos');
      setAvailableVideos(response.data.embeddings || []);
    } catch (error) {
      console.error('Error fetching available videos:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle video selection
  const toggleVideoSelection = (video) => {
    if (selectedVideos.includes(video)) {
      setSelectedVideos(selectedVideos.filter(v => v !== video));
    } else {
      setSelectedVideos([...selectedVideos, video]);
    }
  };

  // Handle chat ID input change
  const handleChatIdChange = (e) => {
    setChatId(e.target.value);
  };

  // Create a new chat with the selected videos
  const createChat = async () => {
    if (!chatId.trim()) {
      alert('Please enter a chat ID');
      return;
    }

    if (selectedVideos.length === 0) {
      alert('Please select at least one video');
      return;
    }

    try {
      setIsLoading(true);
      
      // First, merge the selected videos
      const mergeResponse = await axios.post('http://localhost:8000/merge_embeddings', {
        file_names: selectedVideos,
        context_id: chatId
      });

      // Then, notify the parent component
      if (onVideosSelected) {
        onVideosSelected(chatId, selectedVideos);
      }

      // Reset the form
      setSelectedVideos([]);
      setChatId('');
      setIsOpen(false);
    } catch (error) {
      console.error('Error creating chat:', error);
      alert('Failed to create chat. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500"
      >
        <span>Select Videos for Chat</span>
        {isOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="p-3">
            <div className="mb-3">
              <label htmlFor="chatId" className="block text-sm font-medium text-gray-700">
                Chat ID
              </label>
              <input
                type="text"
                id="chatId"
                value={chatId}
                onChange={handleChatIdChange}
                className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="Enter a unique ID for this chat"
              />
            </div>

            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Available Videos ({availableVideos.length})
              </label>
              {isLoading ? (
                <div className="flex justify-center py-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>
                </div>
              ) : (
                <div className="mt-1 max-h-40 overflow-y-auto">
                  {availableVideos.length === 0 ? (
                    <p className="text-sm text-gray-500">No videos available</p>
                  ) : (
                    <ul className="space-y-1">
                      {availableVideos.map((video) => (
                        <li key={video} className="flex items-center">
                          <button
                            onClick={() => toggleVideoSelection(video)}
                            className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded-md ${
                              selectedVideos.includes(video)
                                ? 'bg-indigo-100 text-indigo-700'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            <span className="truncate">{video}</span>
                            {selectedVideos.includes(video) && (
                              <Check className="w-4 h-4 text-indigo-600" />
                            )}
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div>
                <span className="text-xs text-gray-500">
                  {selectedVideos.length} video(s) selected
                </span>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-3 py-1 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={createChat}
                  disabled={isLoading || selectedVideos.length === 0 || !chatId.trim()}
                  className={`px-3 py-1 text-sm text-white rounded-md ${
                    isLoading || selectedVideos.length === 0 || !chatId.trim()
                      ? 'bg-indigo-300 cursor-not-allowed'
                      : 'bg-indigo-600 hover:bg-indigo-700'
                  }`}
                >
                  {isLoading ? 'Creating...' : 'Create Chat'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoSelector;
