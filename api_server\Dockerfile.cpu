# CPU-only version of the server Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    libsndfile1-dev \
    git \
    wget \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements file first to leverage Docker cache
COPY server_requirements.txt .

# Install CPU-only PyTorch first
RUN pip install torch==2.5.1+cpu torchaudio==2.5.1+cpu torchvision==0.20.1+cpu --index-url https://download.pytorch.org/whl/cpu

# Install other Python dependencies
RUN pip install -r server_requirements.txt

# Create necessary directories
RUN mkdir -p transcripts videos audio_output audio_only

# Copy the application code
COPY . .

# Create a sample speaker audio file (placeholder)
RUN mkdir -p audio_only && echo "placeholder" > audio_only/bill_irwin.wav

# Expose the port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Command to run the application
CMD ["python", "server.py"]
