# CPU-only version of the server Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    libsndfile1-dev \
    portaudio19-dev \
    libasound2-dev \
    espeak-ng \
    git \
    wget \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements file first to leverage Docker cache
COPY server_requirements.txt .

# Create a modified requirements file for CPU-only installation
RUN sed 's/torch==2.5.1+cu121/torch==2.5.1/g; s/torchaudio==2.5.1+cu121/torchaudio==2.5.1/g; s/torchvision==0.20.1+cu121/torchvision==0.20.1/g; s/numpy==1.22.0/numpy>=1.23.5/g; s/networkx==2.8.8/networkx==2.8.8/g; s/scikit-image==0.25.2/scikit-image==0.22.0/g' server_requirements.txt > server_requirements_cpu.txt

# Install CPU-only PyTorch and other dependencies
RUN pip install torch==2.5.1 torchaudio==2.5.1 torchvision==0.20.1 --index-url https://download.pytorch.org/whl/cpu
RUN pip install -r server_requirements_cpu.txt

# Create necessary directories
RUN mkdir -p transcripts videos audio_output audio_only

# Copy the application code
COPY . .

# Create CPU-compatible server.py by modifying CUDA references
RUN sed 's/\.to("cuda")/\.to("cpu")/g; s/device = "cuda"/device = "cpu"/g; s/compute_type = "float16"/compute_type = "float32"/g; s/if device == "cuda":/if False:  # CPU-only mode/g; s/torch\.cuda\.empty_cache()/pass  # CPU-only mode/g' server.py > server_cpu.py

# Create a sample speaker audio file (placeholder)
RUN mkdir -p audio_only && echo "placeholder" > audio_only/bill_irwin.wav

# Expose the port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Command to run the CPU-compatible application
CMD ["python", "server_cpu.py"]
