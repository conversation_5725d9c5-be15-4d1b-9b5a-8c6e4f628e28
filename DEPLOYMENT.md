# 🚀 HPE Video Intelligence Platform - Deployment Guide

## 📋 Prerequisites

1. **Docker & Docker Compose** installed
2. **Docker Hub account** (free at [hub.docker.com](https://hub.docker.com))
3. **GROQ API Key** (free at [console.groq.com](https://console.groq.com/keys))

## 🔧 Environment Setup

### 1. Configure Environment Variables

```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your credentials
# Required:
GROQ_API_KEY=your_actual_groq_api_key
DOCKER_USERNAME=your_dockerhub_username
```

## 🏗️ Building and Uploading to Docker Hub

### 1. Login to Docker Hub

```bash
docker login
# Enter your Docker Hub username and password
```

### 2. Build Images

```bash
# Build all images locally
docker-compose -f docker-compose.cpu.yml build
```

### 3. Tag Images for Docker Hub

```bash
# Replace 'your-dockerhub-username' with your actual username
docker tag hpe-project-frontend your-dockerhub-username/hpe-frontend:latest
docker tag hpe-project-server your-dockerhub-username/hpe-server:latest
docker tag hpe-project-faiss_backend your-dockerhub-username/hpe-faiss-backend:latest

# Optional: Tag with version numbers
docker tag hpe-project-frontend your-dockerhub-username/hpe-frontend:v1.0
docker tag hpe-project-server your-dockerhub-username/hpe-server:v1.0
docker tag hpe-project-faiss_backend your-dockerhub-username/hpe-faiss-backend:v1.0
```

### 4. Push to Docker Hub

```bash
# Push latest tags
docker push your-dockerhub-username/hpe-frontend:latest
docker push your-dockerhub-username/hpe-server:latest
docker push your-dockerhub-username/hpe-faiss-backend:latest

# Push version tags (if created)
docker push your-dockerhub-username/hpe-frontend:v1.0
docker push your-dockerhub-username/hpe-server:v1.0
docker push your-dockerhub-username/hpe-faiss-backend:v1.0
```

## 🌐 Deployment Options

### Option 1: Local Development

```bash
# Use local build
docker-compose -f docker-compose.cpu.yml up -d
```

### Option 2: Production (Docker Hub Images)

```bash
# Use images from Docker Hub
docker-compose -f docker-compose.prod.yml up -d
```

### Option 3: Quick Deploy Script

Create a `deploy.sh` script:

```bash
#!/bin/bash
# Set your Docker Hub username
export DOCKER_USERNAME=your-dockerhub-username
export GROQ_API_KEY=your_groq_api_key

# Pull latest images and start
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## 🔍 Verification

### Check Container Status
```bash
docker-compose -f docker-compose.prod.yml ps
```

### Test Endpoints
```bash
# Health checks
curl http://localhost:8000/health
curl http://localhost:8001/health
curl http://localhost/

# Test video access
curl -I http://localhost:8000/videos/god.mp4
```

## 📱 Access Points

- **Frontend**: http://localhost (Port 80)
- **API Server**: http://localhost:8000
- **FAISS Backend**: http://localhost:8001

## 🛠️ Troubleshooting

### Common Issues

1. **GROQ API Key not set**:
   ```bash
   # Check if environment variable is loaded
   docker-compose -f docker-compose.prod.yml config
   ```

2. **Images not found**:
   ```bash
   # Verify images exist on Docker Hub
   docker pull your-dockerhub-username/hpe-frontend:latest
   ```

3. **Port conflicts**:
   ```bash
   # Check what's using the ports
   netstat -tulpn | grep :80
   netstat -tulpn | grep :8000
   netstat -tulpn | grep :8001
   ```

### Logs

```bash
# View logs for all services
docker-compose -f docker-compose.prod.yml logs

# View logs for specific service
docker-compose -f docker-compose.prod.yml logs frontend
docker-compose -f docker-compose.prod.yml logs server
docker-compose -f docker-compose.prod.yml logs faiss_backend
```

## 🔄 Updates

### Updating Images

```bash
# Pull latest images
docker-compose -f docker-compose.prod.yml pull

# Restart with new images
docker-compose -f docker-compose.prod.yml up -d
```

### Rolling Back

```bash
# Use specific version
export DOCKER_TAG=v1.0
docker-compose -f docker-compose.prod.yml up -d
```

## 🌍 Cloud Deployment

### AWS ECS
1. Push images to Amazon ECR
2. Create ECS task definitions
3. Deploy using ECS service

### Google Cloud Run
1. Push to Google Container Registry
2. Deploy each service to Cloud Run
3. Configure networking

### Azure Container Instances
1. Push to Azure Container Registry
2. Deploy using container groups

## 📊 Monitoring

### Health Checks
All services include health checks that run every 60 seconds.

### Resource Usage
```bash
# Monitor resource usage
docker stats
```

## 🔒 Security Notes

- Change default ports in production
- Use environment-specific API keys
- Enable HTTPS with reverse proxy (nginx/traefik)
- Restrict CORS origins in production
