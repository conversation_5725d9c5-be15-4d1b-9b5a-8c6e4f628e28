[{"text": "Next let us see how to write an algorithm. Here already I have written an algorithm for swapping two numbers or two elements. See I have used C language like syntax only. Only the thing I have done is here I wrote algorithm to show that okay I am writing an algorithm it's not a program.", "start_time": 0.24, "end_time": 17.92}, {"text": "Here if we compare with the C program, here I have not written the data type of parameters. Yes, data types we don't decide at algorithm time. When we write a program then we decide what data types are required. And here even I don't have a temporary variable declaration. Declaration is not there, directly it is used. It's understood unless you declare in the program you cannot use it. But in algorithm we don't bother about those minor things that are related to language.", "start_time": 18.36, "end_time": 47.02}, {"text": "This is more like C. Now if you want to write in different way, you can say this as begin, this as end. In stock lower back edge you can also say begin and end. Next, you can use this symbol also for assignment. Actually this is an assignment symbol that was used in some more languages like Pascal language. So you can use that one. Or else you can use this symbol", "start_time": 49.1, "end_time": 89.54}, {"text": "For showing that this value is stored at this one. So now it's up to you how you want to write it. I hope whatever I wrote you have understood it. So same way whatever you write I should be able to understand at least you should be able to understand it. Or the people who are involved in the project should be able to understand it. Who are using it should be able to understand it. Right? So you can use any syntax there is no fixed syntax for writing any allegorata.", "start_time": 90.66, "end_time": 118.94}, {"text": "Let's see how to analyze an algorithm, what is the criteria on which we analyze an algorithm. So the criteria is, the first criteria is, see the algorithms are the procedures for solving problems, whether you do it manually using pen and paper or you make a program and let your machines do it. Whatever the method may be,", "start_time": 119.36, "end_time": 146.22}, {"text": "How much time it is taking? If the procedure is very lengthy and time consuming or whether the procedure is very fast and quickly you can get the results. So that is one of the important criteria on which we have to analyze. If you are devising a procedure or an algorithm then it should be time efficient means it must be faster. So after writing the algorithms we analyze how much time it is taking. So that time what we get is in the form of a function.", "start_time": 146.52, "end_time": 177.02}, {"text": "We get a function, time function. We not get the watch time already I have shown you. You will get the function, time function. Now next is space. As the algorithm is going to be converted into a program and it's going to run on the machine then we need to know how much memory space it will consume. So that is the second criteria on which we will analyze in the quarter.", "start_time": 177.38, "end_time": 209.2}, {"text": "So these are the two major criteria on which the algorithms are analyzed. And furthermore if you have any other criteria or criteria and furthermore if you have any other criteria or any other factors you want to consider you can consider them like. Now", "start_time": 209.42, "end_time": 235.2}, {"text": "Nowadays every application is either internet based or web based or say cloud based. So data transfer or network consumption is also an important criteria. How much data is going to be transferred?", "start_time": 236.08, "end_time": 250.96}, {"text": "See if the algorithm or means if a procedure that you are writing if it is not necessary transferring larger size data or it can be reduced or compressed whatever it is. So that is one more important criteria that is how much data transfer is done. Nowadays most of the devices are handled instead of PCs we are using palm tops and laptops and tablets then power consumption is also a criteria. How much power it is consuming?", "start_time": 251.58, "end_time": 285.86}, {"text": "And one more criteria is if you are developing an algorithm for device drivers or system level programming, if you are developing something, some algorithm if you are writing, then you may also need to know how much CPU registers it is consuming.", "start_time": 288.5, "end_time": 311.08}, {"text": "So CPU will also have some memory called as registers. How many registers it is consuming? That's also one of the worrying factor when you are designing an Elgort or writing a program for it. So these are few criteria I have listed and it depends on your requirement, it depends on your project, right? What are the more criteria you need to analyze? Now let us see, I have taken one example to show you how we analyze an Elgort.", "start_time": 311.48, "end_time": 340.58}, {"text": "So for an answer purpose we need to know the time. So first we do time analysis then we do space analysis. Now time means how much time this will take. This is not the wash time then how to assume. Now we say that every statement in an algorithm takes one unit of time. Every simple statement in an algorithm takes one unit of time.", "start_time": 340.82, "end_time": 365.84}, {"text": "If suppose the algorithm is calling another algorithm, it's using another procedure then you have to analyze that also in detail. So simple means just statements, direct statements, it's not a nested statement. So single statement each statement takes one unit of time. So here I assume it takes one unit of time, one unit of time, one unit. So problem time is three. So the time function f of n is three. Here we got the constant value", "start_time": 366.04, "end_time": 394.76}, {"text": "because I have taken a very simple algorithm and the answer is 3 that is some constant value right 3 or 30 whatever it is it is constant value fixed value we got so there's a time function it is not in terms of n just we got a constant value. Now here whatever the complexity in the statement may be how lengthy the statement will be we say that it takes volume of time let us assume instead of this statement I have some statement saying", "start_time": 395.02, "end_time": 426.36}, {"text": "x assigned 5 into plus 6 into if this is the statement written in an algorithm. Then for this also we say it takes 1 unit of time but really if you see when you convert into a program and finally when it gets converted into machine code then how many statements it will have", "start_time": 426.84, "end_time": 451.5}, {"text": "for each multiplication, two statements, then for addition one statement, then assignment one statement. So total four statements will be there but we don't want to go into that much detail, we don't want to see how it's going to get converted to machine code but simply we analyze and say each statement takes one year of time. Now suppose if you want to go into detail analysis like this, you can go into detail.", "start_time": 451.88, "end_time": 481.0}, {"text": "You can imagine up to the level of the machine code that's going to be generated. It is just like, suppose you want to travel to your friend's house in a car. You don't have to think and you don't have to plan and analyze. Simply you can take a car or a bike or a vehicle and you can just start and reach his place. Right? That's simple. If you want to land on Mars,", "start_time": 481.68, "end_time": 509.26}, {"text": "Simply you cannot take a satellite so you cannot take a rocket and launch it and reach there. It's not easy. So you have to analyze and design and make a complete plan so that your mission is successful. So that's it. Here you need to go into each and every minor detail you have to take.", "start_time": 510.74, "end_time": 530.8}, {"text": "So it depends on your requirement. So if you want to analyze into greater detail then do it. But something point of view we will be doing it at basic level right. At a very shallow level this brief analysis we will do will not go into much detail but much detail analysis can also be done. Next is space analysis.", "start_time": 532.0, "end_time": 556.8}, {"text": "What are the variables used here? For a space I will write down the variables a is one parameter b is also a parameter and temp is a local variable used. So to tell how many variables 1, 2 and 3. So there are three variables used. So the space is constant again. It is just 3. I got the answer as 3. So again it is also constant.", "start_time": 557.92, "end_time": 584.76}, {"text": "So usually we represent them as order of one, constant is order of one and that's also order of one means it is constant one represents constant if it is three also we write one if it is three thousand also we write one saying that it is constant value constant. So this how each statement is taken as one unit of time and each variable is taken as one word so these are", "start_time": 585.7, "end_time": 617.42}, {"text": "3 words. Why we are saying words there? Not bytes. We don't know. When we converted to a program, it may be an integer type or float type or double type that we don't know. So that's why we say it is taking words.", "start_time": 620.14, "end_time": 636.16}]