# HPE Video Intelligence Platform

A comprehensive video intelligence platform that combines speech-to-text transcription, vector search, and AI-powered chat functionality using Retrieval-Augmented Generation (RAG).

## 🚀 Features

- **Speech to Text**: Whisper/whisper-s2t for accurate transcription
- **Vector Database**: FAISS for efficient similarity search
- **AI Chat**: GROQ API integration for intelligent responses
- **Frontend**: Modern React interface with video playback
- **Backend**: FastAPI with Docker containerization
- **Text-to-Speech**: Generate audio responses
- **YouTube Integration**: Process YouTube videos directly

## 🐳 Quick Start with Docker (Recommended)

### Prerequisites
- Docker and Docker Compose installed
- At least 8GB of available RAM

### Run the Application

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd HPE-project
   ```

2. **Start with Docker (CPU-only version):**
   ```bash
   docker-compose -f docker-compose.cpu.yml up --build
   ```

3. **Access the application:**
   - **Frontend**: http://localhost
   - **API Server**: http://localhost:8000
   - **FAISS Backend**: http://localhost:8001

For detailed Docker setup instructions, see [DOCKER_README.md](DOCKER_README.md)

## 🛠️ Manual Setup (Development)

### API Server
[Detailed instructions](api_server/README.md)

```bash
cd api_server
pip install -r server_requirements.txt
python server_cpu.py
```

### Frontend
[Detailed instructions](frontend/README.md)

```bash
cd frontend
npm install
npm run dev
```

## 📁 Project Structure

```
HPE-project/
├── api_server/           # FastAPI backend services
│   ├── server_cpu.py     # Main API server (CPU optimized)
│   ├── new_faiss_backend.py  # FAISS vector search service
│   └── Dockerfile.cpu    # Docker configuration
├── frontend/             # React frontend application
├── docker-compose.cpu.yml   # Docker Compose (CPU-only)
├── docker-compose.yml       # Docker Compose (GPU version)
└── DOCKER_README.md         # Detailed Docker instructions
```

## 🔧 Technology Stack

- **Backend**: FastAPI, Python
- **Frontend**: React, Vite, TailwindCSS
- **AI/ML**: Whisper, FAISS, GROQ API, HuggingFace
- **Containerization**: Docker, Docker Compose
- **Web Server**: Nginx (for frontend)

## 📖 Documentation

- [Docker Setup Guide](DOCKER_README.md)
- [API Server Documentation](api_server/README.md)
- [Frontend Documentation](frontend/README.md)