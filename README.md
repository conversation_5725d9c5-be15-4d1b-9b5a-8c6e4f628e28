# HPE Video Intelligence Platform

A comprehensive video intelligence platform that combines speech-to-text transcription, vector search, and AI-powered chat functionality using Retrieval-Augmented Generation (RAG).

## 🚀 Features

- **Speech to Text**: Whisper/whisper-s2t for accurate transcription
- **Vector Database**: FAISS for efficient similarity search
- **AI Chat**: GROQ API integration for intelligent responses
- **Frontend**: Modern React interface with video playback
- **Backend**: FastAPI with Docker containerization
- **Text-to-Speech**: Generate audio responses
- **YouTube Integration**: Process YouTube videos directly

## 🐳 Quick Start with Docker (Recommended)

### Prerequisites
- Docker and Docker Compose installed
- At least 8GB of available RAM
- GROQ API Key (free at [console.groq.com](https://console.groq.com/keys))

### Option 1: Production Deployment (Docker Hub)

1. **Download the deployment files:**
   - `docker-compose.prod.yml`
   - `.env.example`
   - `deploy.bat` (Windows) or `deploy.sh` (Linux/Mac)

2. **Configure environment:**
   ```bash
   # Copy environment template
   cp .env.example .env

   # Edit .env file and set:
   # GROQ_API_KEY=your_groq_api_key_here
   # DOCKER_USERNAME=your_dockerhub_username
   ```

3. **Deploy (Windows):**
   ```cmd
   deploy.bat
   ```

   **Deploy (Linux/Mac):**
   ```bash
   ./deploy.sh
   ```

### Option 2: Local Development

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd HPE-project
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your GROQ_API_KEY
   ```

3. **Start with Docker (CPU-only version):**
   ```bash
   docker-compose -f docker-compose.cpu.yml up --build
   ```

### Access Points
- **Frontend**: http://localhost (Split-screen UI with video player)
- **API Server**: http://localhost:8000
- **FAISS Backend**: http://localhost:8001

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md)

## 🛠️ Manual Setup (Development)

### API Server
[Detailed instructions](api_server/README.md)

```bash
cd api_server
pip install -r server_requirements.txt
python server_cpu.py
```

### Frontend
[Detailed instructions](frontend/README.md)

```bash
cd frontend
npm install
npm run dev
```

## 📁 Project Structure

```
HPE-project/
├── api_server/           # FastAPI backend services
│   ├── server_cpu.py     # Main API server (CPU optimized)
│   ├── new_faiss_backend.py  # FAISS vector search service
│   └── Dockerfile.cpu    # Docker configuration
├── frontend/             # React frontend application
├── docker-compose.cpu.yml   # Docker Compose (CPU-only)
├── docker-compose.yml       # Docker Compose (GPU version)
└── DOCKER_README.md         # Detailed Docker instructions
```

## 🔧 Technology Stack

- **Backend**: FastAPI, Python
- **Frontend**: React, Vite, TailwindCSS
- **AI/ML**: Whisper, FAISS, GROQ API, HuggingFace
- **Containerization**: Docker, Docker Compose
- **Web Server**: Nginx (for frontend)

## 🌟 Key Features

### Split-Screen Interface
- **Dynamic Layout**: Starts with full-width chat interface
- **Video Panel**: Appears when clicking on transcript timestamps
- **Resizable Panels**: Drag to adjust chat/video panel sizes
- **Video Playback**: Integrated video player with transcript synchronization

### Video Intelligence
- **Upload Videos**: Support for MP4 files and YouTube URLs
- **Auto Transcription**: Whisper-powered speech-to-text
- **Semantic Search**: FAISS vector database for intelligent content search
- **AI Chat**: RAG-powered conversations about video content

### Easy Deployment
- **Docker Hub Ready**: Pre-built images for instant deployment
- **Environment Variables**: Configurable API keys per user
- **Health Checks**: Automatic service monitoring
- **Cross-Platform**: Windows, Linux, and Mac support

## 📖 Documentation

- [Complete Deployment Guide](DEPLOYMENT.md) - **Start here for production deployment**
- [Docker Setup Guide](DOCKER_README.md)
- [API Server Documentation](api_server/README.md)
- [Frontend Documentation](frontend/README.md)

## 🚀 For Developers

### Upload to Docker Hub
See [DEPLOYMENT.md](DEPLOYMENT.md) for complete instructions on building and uploading your own images.

### Environment Configuration
All sensitive configuration is handled through environment variables:
- `GROQ_API_KEY`: Your GROQ API key for LLM functionality
- `DOCKER_USERNAME`: Your Docker Hub username for image deployment