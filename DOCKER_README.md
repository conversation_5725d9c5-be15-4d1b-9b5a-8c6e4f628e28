# HPE Project Docker Setup

This document provides instructions for running the HPE project using Docker containers.

## Architecture

The application consists of three main services:

1. **Server** (`api_server`) - Main API server running on port 8000
   - Handles audio transcription using Whisper
   - Provides text-to-speech functionality
   - Manages video processing and YouTube integration
   - Requires GPU support for optimal performance

2. **FAISS Backend** (`faiss_backend`) - Vector search service running on port 8001
   - Handles embeddings and vector search
   - Provides chat functionality with RAG
   - Manages FAISS indexes

3. **Frontend** - React/Vite application running on port 80
   - User interface for the application
   - Served using Nginx

## Prerequisites

- Docker and Docker Compose installed
- NVIDIA Docker runtime (for GPU support)
- At least 8GB of available RAM
- NVIDIA GPU with CUDA support (recommended)

## Quick Start

### Option 1: CPU-Only Version (Recommended for most users)

1. **Clone the repository and navigate to the project directory:**
   ```bash
   cd HPE-project
   ```

2. **Build and start all services (CPU-only):**
   ```bash
   docker-compose -f docker-compose.cpu.yml up --build
   ```

3. **Access the application:**
   - Frontend: http://localhost
   - Server API: http://localhost:8000
   - FAISS Backend API: http://localhost:8001

### Option 2: GPU Version (Requires NVIDIA GPU)

1. **Build and start all services (GPU):**
   ```bash
   docker-compose up --build
   ```

2. **Access the application:**
   - Frontend: http://localhost
   - Server API: http://localhost:8000
   - FAISS Backend API: http://localhost:8001

## Individual Service Management

### Build specific services:
```bash
# Build only the server
docker-compose build server

# Build only the FAISS backend
docker-compose build faiss_backend

# Build only the frontend
docker-compose build frontend
```

### Run specific services:
```bash
# Run only server and faiss_backend
docker-compose up server faiss_backend

# Run in detached mode
docker-compose up -d
```

### Stop services:
```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## Configuration

### Environment Variables

The services use the following environment variables:

**Server:**
- `BACKEND_EMBED_URL`: URL for the FAISS backend (default: http://faiss_backend:8001/embed)
- `CUDA_VISIBLE_DEVICES`: GPU device to use (default: 0)

**FAISS Backend:**
- No specific environment variables required

### Volumes

The following directories are mounted as volumes for data persistence:
- `./api_server/transcripts` - Stored transcripts
- `./api_server/videos` - Video files
- `./api_server/audio_output` - Generated audio files
- `./api_server/embeddings` - FAISS embeddings
- `./api_server/chat_mappings` - Chat to video mappings

## GPU Support

The server container requires NVIDIA GPU support for optimal performance. Make sure you have:

1. NVIDIA drivers installed on the host
2. NVIDIA Docker runtime installed
3. Docker Compose version that supports GPU reservations

If you don't have GPU support, you can modify the server Dockerfile to use CPU-only versions of PyTorch.

## Health Checks

All services include health checks:
- Server: `http://localhost:8000/health`
- FAISS Backend: `http://localhost:8001/health`
- Frontend: `http://localhost:80`

## Troubleshooting

### Common Issues:

1. **GPU not available:**
   - Ensure NVIDIA Docker runtime is installed
   - Check if GPU is accessible: `docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi`

2. **Port conflicts:**
   - Make sure ports 80, 8000, and 8001 are not in use
   - Modify port mappings in docker-compose.yml if needed

3. **Memory issues:**
   - Ensure sufficient RAM is available
   - Consider reducing model sizes or batch sizes

4. **Build failures:**
   - Check Docker logs: `docker-compose logs [service_name]`
   - Ensure all requirements files are present

### Logs:
```bash
# View logs for all services
docker-compose logs

# View logs for specific service
docker-compose logs server
docker-compose logs faiss_backend
docker-compose logs frontend

# Follow logs in real-time
docker-compose logs -f
```

## Development

For development purposes, you can mount the source code as volumes to enable hot reloading:

```yaml
# Add to docker-compose.yml under the server service
volumes:
  - ./api_server:/app
  - ./api_server/transcripts:/app/transcripts
  # ... other volumes
```

## Production Deployment

For production deployment, consider:

1. Using specific image tags instead of building from source
2. Setting up proper secrets management
3. Configuring reverse proxy (nginx/traefik)
4. Setting up monitoring and logging
5. Using Docker Swarm or Kubernetes for orchestration
