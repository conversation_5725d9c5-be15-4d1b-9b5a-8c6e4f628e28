from fastapi import Fast<PERSON><PERSON>, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import whisper_s2t
import torch
import time
import gc
import uvicorn
import httpx
import os
import json
import requests
from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional
import pytube
import re
from TTS.api import TTS
import uuid
import subprocess

tts = TTS("tts_models/en/vctk/vits").to("cpu")  # CPU-only mode
SPEAKER_WAV = "./audio_only/bill_irwin.wav"
SPEAKER_ID = "p236"

# Use environment variable for backend URL, default to Docker service name
BACKEND_EMBED_URL = os.getenv("BACKEND_EMBED_URL", "http://faiss_backend:8001/embed")

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/audio_output", StaticFiles(directory="audio_output"), name="audio_output")
app.mount("/videos", StaticFiles(directory="videos"), name="videos")

device = "cuda"
# device = "cpu"
compute_type = "float16"
# compute_type = "float32"
batch_size = 8
max_speech_len = 29 # default
model = whisper_s2t.load_model("small", backend="CTranslate2", device=device, compute_type=compute_type, max_speech_len = max_speech_len)

# Create directories if they don't exist
transcripts_dir = "transcripts"
videos_dir = "videos"
os.makedirs(transcripts_dir, exist_ok=True)
os.makedirs(videos_dir, exist_ok=True)

class TranscriptMeta(BaseModel):
    id: str
    filename: str
    timestamp: str
    duration: Optional[float] = None
    length: Optional[int] = None

class TranscriptWithContent(TranscriptMeta):
    transcription: str

class LinkRequest(BaseModel):
    url: str
    context_id: str = "default"

# In-memory list to store transcript metadata
transcripts_index = []

# Load existing transcripts metadata on startup
def load_transcripts_index():
    index_path = os.path.join(transcripts_dir, "index.json")
    if os.path.exists(index_path):
        with open(index_path, "r") as f:
            return json.load(f)
    return []

# Save transcripts index
def save_transcripts_index():
    index_path = os.path.join(transcripts_dir, "index.json")
    with open(index_path, "w") as f:
        json.dump(transcripts_index, f, indent=4)

# Extract YouTube video ID from various URL formats
def extract_youtube_id(url):
    # Patterns to match different YouTube URL formats
    patterns = [
        r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
        r'youtube\.com\/watch\?v=([^&]+)',
        r'youtu\.be\/([^?]+)'
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    return None

def clean_transcription_data(data):
    cleaned_data = []

    for item in data:
        cleaned_item = {
            key: value for key, value in item.items()
            if key not in ['avg_logprob', 'no_speech_prob']
        }
        cleaned_data.append(cleaned_item)

    return cleaned_data

transcripts_index = load_transcripts_index()

@app.post("/transcribe/")
async def transcribe_audio(file: UploadFile = File(...), speech_len: int = Form(29), context_id: str = Form("default")):
    print(f"Received file: {file.filename}, speech_len: {speech_len}")

    global max_speech_len
    global model

    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    safe_filename = file.filename.replace(' ', '_')
    unique_id = f"{safe_filename}"

    # Read file content once
    file_content = await file.read()

    temp_file = f"temp_{safe_filename}"
    with open(temp_file, "wb") as buffer:
        buffer.write(file_content)

    ext = os.path.splitext(file.filename)[1].lower()

    # Save video file if it's a video format
    video_path = None
    if ext in [".mp4", ".mkv", ".mov", ".avi"]:
        # Save the original video file
        video_filename = f"{unique_id}"
        video_path = os.path.join(videos_dir, video_filename)
        with open(video_path, "wb") as video_buffer:
            video_buffer.write(file_content)
        print(f"Saved video file: {video_path}")

        # Convert to audio for transcription
        converted_temp_file = f"{temp_file}.mp3"
        ffmpeg_cmd = [
            "ffmpeg", "-i", temp_file,
            "-vn", "-acodec", "libmp3lame", "-y", converted_temp_file
        ]
        try:
            subprocess.run(ffmpeg_cmd, check=True)
        except subprocess.CalledProcessError:
            raise HTTPException(status_code=500, detail="Error extracting audio with ffmpeg")

        os.remove(temp_file)
        temp_file = converted_temp_file

    print(f"Ready to process audio: {temp_file}")

    start = time.time()

    files = [temp_file]
    lang_codes = ["en"]
    tasks = ["transcribe"]
    initial_prompts = [None]


    print(f"Reinitializing model with new max_speech_len: {speech_len}  {max_speech_len}")

    if(speech_len != max_speech_len):
        print(f"Reinitializing model with new max_speech_len: {speech_len}")
        del model
        gc.collect()

        if device == "cuda":
            torch.cuda.empty_cache()

        max_speech_len = speech_len
        model = whisper_s2t.load_model("small", backend="CTranslate2", device=device, compute_type=compute_type, max_speech_len = speech_len)
        print(f"Reinitialized model with new max_speech_len: {max_speech_len}")
    out = model.transcribe_with_vad(files, lang_codes=lang_codes, tasks=tasks, initial_prompts=initial_prompts, batch_size=batch_size)

    # Create transcript-specific directory
    transcript_dir = os.path.join(transcripts_dir, unique_id)
    os.makedirs(transcript_dir, exist_ok=True)

    # Save VTT format
    vtt_path = os.path.join(transcript_dir, "transcript.vtt")
    whisper_s2t.write_outputs(out, format='vtt', op_files=[vtt_path])

    # Save JSON format
    json_path = os.path.join(transcript_dir, "transcript.json")
    whisper_s2t.write_outputs(out, format='json', op_files=[json_path])

    # Clean and update JSON
    with open(json_path, "r") as f:
        data = json.load(f)

    cleaned_data = clean_transcription_data(data)
    with open(json_path, "w") as f:
        json.dump(cleaned_data, f, indent=4)

    # Read plain text transcript
    with open(vtt_path, "r") as f:
        vtt_content = f.read()
    transcript_text = "\n".join([item["text"] for item in cleaned_data])

    # Save plain text format
    txt_path = os.path.join(transcript_dir, "transcript.txt")
    with open(txt_path, "w") as f:
        f.write(transcript_text)

    # Clean up temporary file
    os.remove(temp_file)

    end = time.time()
    processing_time = end - start
    print(f"\nTime taken for transcription: {processing_time}")

    # Create metadata entry
    transcript_meta = {
        "id": unique_id,
        "filename": file.filename,
        "timestamp": datetime.now().isoformat(),
        "duration": processing_time,
        "length": len(transcript_text),
        "video_path": video_path if video_path else None,
        "file_type": "video" if video_path else "audio"
    }

    # Add to index and save
    transcripts_index.append(transcript_meta)
    save_transcripts_index()

    gc.collect()
    torch.cuda.empty_cache()

    # Extract base filename without extension for embedding
    base_filename = os.path.splitext(os.path.basename(file.filename))[0]
    # Replace spaces and special characters for safe storage
    base_filename = re.sub(r'[^\w\-_]', '_', base_filename)

    async with httpx.AsyncClient() as client:
        try:
            embed_response = await client.post(BACKEND_EMBED_URL, json={"file_path": json_path, "context_id": base_filename, "title": safe_filename})
            embed_response.raise_for_status()
            embed_result = embed_response.json()
        except httpx.HTTPStatusError as e:
            raise HTTPException(status_code=e.response.status_code, detail=f"Embedding failed: {e.response.text}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error contacting embedding service: {str(e)}")

    return {
        "id": unique_id,
        "transcription": transcript_text,
        "filename": file.filename,
        "timestamp": transcript_meta["timestamp"]
    }

@app.delete("/transcripts/{transcript_id}")
async def delete_transcript(transcript_id: str, speech_len: int = 29):
    # Find transcript in index
    transcript_index = next((i for i, t in enumerate(transcripts_index) if t["id"] == transcript_id), None)

    if transcript_index is None:
        raise HTTPException(status_code=404, detail="Transcript not found")

    # Get transcript metadata before removing
    removed_transcript = transcripts_index[transcript_index]

    # Remove from index
    transcripts_index.pop(transcript_index)
    save_transcripts_index()

    # Remove transcript files
    transcript_dir = os.path.join(transcripts_dir, transcript_id)
    if os.path.exists(transcript_dir):
        import shutil
        shutil.rmtree(transcript_dir)

    # Remove associated video file if it exists
    video_path = removed_transcript.get('video_path')
    if video_path and os.path.exists(video_path):
        try:
            os.remove(video_path)
            print(f"Deleted associated video file: {video_path}")
        except Exception as e:
            print(f"Warning: Could not delete video file {video_path}: {str(e)}")

    return {"message": "Transcript and associated video deleted successfully", "id": transcript_id}

@app.post("/link_transcript/")
async def process_link(link_request: LinkRequest):
    context_id = link_request.context_id
    url = link_request.url

    # Generate a unique ID using timestamp
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

    try:
        # For YouTube links, try to extract video info
        video_id = extract_youtube_id(url)

        if video_id:
            # Process YouTube link
            try:
                # Create a YouTube object and get the video title
                yt = pytube.YouTube(url)
                video_title = yt.title
                safe_title = re.sub(r'[^\w\-_\. ]', '_', video_title)
                unique_id = f"{timestamp}_youtube_{video_id}"

                # Get the audio stream with the lowest size
                audio_stream = yt.streams.filter(only_audio=True).first()

                if not audio_stream:
                    raise HTTPException(status_code=400, detail="Could not find audio stream for this YouTube video")

                # Create temporary directory for download
                temp_dir = "temp_downloads"
                os.makedirs(temp_dir, exist_ok=True)

                # Download the audio file
                print(f"Downloading audio from YouTube video: {video_title}")
                temp_file_path = audio_stream.download(output_path=temp_dir)
                temp_file_name = os.path.basename(temp_file_path)

                start = time.time()

                # Transcribe the downloaded audio
                files = [temp_file_path]
                lang_codes = ["en"]
                tasks = ["transcribe"]
                initial_prompts = [None]

                out = model.transcribe_with_vad(files, lang_codes=lang_codes, tasks=tasks, initial_prompts=initial_prompts, batch_size=batch_size)

                # Create transcript-specific directory
                transcript_dir = os.path.join(transcripts_dir, unique_id)
                os.makedirs(transcript_dir, exist_ok=True)

                # Save VTT format
                vtt_path = os.path.join(transcript_dir, "transcript.vtt")
                whisper_s2t.write_outputs(out, format='vtt', op_files=[vtt_path])

                # Save JSON format
                json_path = os.path.join(transcript_dir, "transcript.json")
                whisper_s2t.write_outputs(out, format='json', op_files=[json_path])

                # Clean and update JSON
                with open(json_path, "r") as f:
                    data = json.load(f)

                cleaned_data = clean_transcription_data(data)
                with open(json_path, "w") as f:
                    json.dump(cleaned_data, f, indent=4)

                # Read plain text transcript
                with open(vtt_path, "r") as f:
                    vtt_content = f.read()
                transcript_text = vtt_content[6:] if vtt_content.startswith("WEBVTT") else vtt_content

                # Save plain text format
                txt_path = os.path.join(transcript_dir, "transcript.txt")
                with open(txt_path, "w") as f:
                    f.write(transcript_text)

                # Clean up temporary file
                os.remove(temp_file_path)

                end = time.time()
                processing_time = end - start
                print(f"\nTime taken for transcription: {processing_time}")

                # Create metadata entry with YouTube info
                transcript_meta = {
                    "id": unique_id,
                    "filename": f"YouTube: {video_title}",
                    "url": url,
                    "video_id": video_id,
                    "timestamp": datetime.now().isoformat(),
                    "duration": processing_time,
                    "length": len(transcript_text)
                }

                # Add to index and save
                transcripts_index.append(transcript_meta)
                save_transcripts_index()

                gc.collect()
                torch.cuda.empty_cache()

                # Use video_id as the context_id for embeddings
                async with httpx.AsyncClient() as client:
                    try:
                        embed_response = await client.post(BACKEND_EMBED_URL, json={"file_path": json_path, "context_id": video_id, "title": safe_title})
                        embed_response.raise_for_status()
                        embed_result = embed_response.json()
                    except httpx.HTTPStatusError as e:
                        raise HTTPException(status_code=e.response.status_code, detail=f"Embedding failed: {e.response.text}")
                    except Exception as e:
                        raise HTTPException(status_code=500, detail=f"Error contacting embedding service: {str(e)}")

                return {
                    "id": unique_id,
                    "transcription": transcript_text,
                    "filename": f"YouTube: {video_title}",
                    "video_id": video_id,
                    "url": url,
                    "timestamp": transcript_meta["timestamp"]
                }

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"YouTube processing error: {str(e)}")
        else:
            # Handle other types of links (non-YouTube)
            unique_id = f"{timestamp}_link_{url.replace('://', '_').replace('/', '_').replace('.', '_')}"
            raise HTTPException(status_code=400, detail="Only YouTube links are supported at this time")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing link: {str(e)}")

@app.get("/transcripts/")
async def get_all_transcripts():
    return transcripts_index

@app.get("/transcripts/{transcript_id}")
async def get_transcript(transcript_id: str):
    # Find transcript in index
    transcript_meta = next((t for t in transcripts_index if t["id"] == transcript_id), None)

    if not transcript_meta:
        return {"error": "Transcript not found"}

    # Read the transcript content
    txt_path = os.path.join(transcripts_dir, transcript_id, "transcript.txt")

    if not os.path.exists(txt_path):
        return {"error": "Transcript file not found"}

    with open(txt_path, "r") as f:
        transcript_text = f.read()

    return {
        **transcript_meta,
        "transcription": transcript_text
    }


class ChatRequest(BaseModel):
    query: str
    top_k: int = 3
    model: str = "llama-3.1-8b-instant"
    context_id: str = "default"
    external_service_url: str = os.getenv("FAISS_BACKEND_URL", "http://faiss_backend:8001") + "/chat"

class ChatResponse(BaseModel):
    response: str
    tts_path: str
    retrieved_contexts: list[str]
    context_metadata: list = []

@app.post("/chat_primary")
async def primary_chat(request: ChatRequest):
    print("primary o")
    try:
        # Use httpx for async HTTP requests
        async with httpx.AsyncClient() as client:
            # Prepare the payload for the external service
            payload = {
                "query": request.query,
                "top_k": request.top_k,
                "model": request.model,
                "context_id": request.context_id
            }

            # Make the async HTTP request to the external chat service
            external_response = await client.post(
                request.external_service_url,
                json=payload,
                timeout=300.0  # 5 minutes timeout for large processing
            )

            # Raise an exception for bad HTTP responses
            external_response.raise_for_status()

            # Parse the response
            response_data = external_response.json()
            tts_result = await text_to_speech({"text": response_data['response']})
            tts_path = tts_result["audio_url"]
            print(tts_result)
            print(tts_path)

            return ChatResponse(
                response=response_data['response'],
                tts_path=tts_path,
                retrieved_contexts=response_data.get('retrieved_contexts', []),
                context_metadata=response_data.get('context_metadata', [])
            )

    except httpx.RequestError as e:
        # Handle network-related errors
        raise HTTPException(
            status_code=503,
            detail=f"External service connection error: {str(e)}"
        )

    except httpx.HTTPStatusError as e:
        # Handle HTTP error responses
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"External service error: {str(e)}"
        )

    except Exception as e:
        # Catch-all for any other unexpected errors
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        )

def generate_tts(text):
    audio_dir = "audio_output"
    os.makedirs(audio_dir, exist_ok=True)

    filename = f"{uuid.uuid4()}.mp3"
    filepath = os.path.join(audio_dir, filename)

    try:
        tts.tts_to_file(
            text=text,
            file_path=filepath,
            speaker=SPEAKER_ID,  # Use the predefined SPEAKER_ID
            speed=1.0,  # Adjust as needed
            pitch=1.0,
            energy=1.0,
            intensity=1.0,
            emphasis=1.0,
            volume=1.0
        )
        return f"./audio_output/{filename}"

    except Exception as e:
        # Log the error and raise an HTTP exception
        print(f"TTS Generation Error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Text-to-speech conversion failed: {str(e)}"
        )

@app.post("/tts")
async def text_to_speech(data: dict):

    # Validate input
    if not data or 'text' not in data:
        raise HTTPException(
            status_code=400,
            detail="No text provided for conversion"
        )

    # Limit text length to prevent excessive processing
    text = data['text']
    if len(text) > 5000:  # Increased max length for larger texts
        text = text[:5000]

    # Generate audio file
    audio_url = generate_tts(text)

    return {
        "audio_url": audio_url
    }


# Optional: Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# @app.get("/saved_embeddings")
# async def get_saved_embeddings():
#     """Get a list of all saved embeddings names without extensions.
#     This checks the embeddings directory and returns unique names (removing file extensions).
#     """
#     try:
#         embeddings_dir = "embeddings"
#         if not os.path.exists(embeddings_dir):
#             return {"embeddings": []}

#         # Get all files in the embeddings directory
#         all_files = os.listdir(embeddings_dir)

#         # Extract base names without extensions and filter out duplicates
#         embedding_names = set()
#         for filename in all_files:
#             # Skip any system files or hidden files
#             if filename.startswith('.') or filename.startswith('__'):
#                 continue

#             # Skip metadata files
#             if filename.endswith('_metadata.pkl'):
#                 continue

#             # Only include .index files to avoid duplicates
#             if filename.endswith('.index'):
#                 # Extract the name without extension
#                 base_name = os.path.splitext(filename)[0]
#                 if base_name:
#                     embedding_names.add(base_name)

#         # Convert to sorted list with GENERAL at the top if present
#         sorted_names = sorted(list(embedding_names))

#         # Always ensure GENERAL is included and at the top
#         if "GENERAL" in sorted_names:
#             sorted_names.remove("GENERAL")
#         sorted_names.insert(0, "GENERAL")

#         return {"embeddings": sorted_names}
#     except Exception as e:
#         print(f"Error getting saved embeddings: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"Error retrieving saved embeddings: {str(e)}")

@app.get("/video_embeddings")
async def get_video_embeddings():
    """Get a list of all available video embeddings.
    This forwards the request to the FAISS backend service.
    """
    try:
        faiss_backend_url = os.getenv("FAISS_BACKEND_URL", "http://faiss_backend:8001")
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{faiss_backend_url}/available_embeddings")
            response.raise_for_status()
            return response.json()
    except Exception as e:
        print(f"Error getting video embeddings: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving video embeddings: {str(e)}")

@app.post("/merge_embeddings")
async def merge_embeddings(request: dict):
    """Merge multiple video embeddings for a chat.
    This forwards the request to the FAISS backend service.
    """
    try:
        faiss_backend_url = os.getenv("FAISS_BACKEND_URL", "http://faiss_backend:8001")
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{faiss_backend_url}/merge_embeddings", json=request)
            response.raise_for_status()
            return response.json()
    except Exception as e:
        print(f"Error merging embeddings: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error merging embeddings: {str(e)}")


# Video endpoints
@app.get("/videos/")
async def get_all_videos():
    """Get a list of all available videos with their metadata."""
    try:
        video_files = []

        # Get all video files from the videos directory
        if os.path.exists(videos_dir):
            for filename in os.listdir(videos_dir):
                if filename.lower().endswith(('.mp4', '.mkv', '.mov', '.avi')):
                    file_path = os.path.join(videos_dir, filename)
                    file_stats = os.stat(file_path)

                    # Find corresponding transcript metadata
                    transcript_meta = None
                    for transcript in transcripts_index:
                        if transcript.get('video_path') == file_path:
                            transcript_meta = transcript
                            break

                    video_info = {
                        "filename": filename,
                        "path": f"/videos/{filename}",
                        "size": file_stats.st_size,
                        "created": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                        "transcript_id": transcript_meta.get('id') if transcript_meta else None,
                        "has_transcript": transcript_meta is not None
                    }
                    video_files.append(video_info)

        return {"videos": video_files}
    except Exception as e:
        print(f"Error getting videos: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving videos: {str(e)}")

@app.get("/videos/{video_filename}")
async def get_video_info(video_filename: str):
    """Get information about a specific video file."""
    try:
        video_path = os.path.join(videos_dir, video_filename)
        print(video_path)

        if not os.path.exists(video_path):
            raise HTTPException(status_code=404, detail="Video not found")

        file_stats = os.stat(video_path)

        # Find corresponding transcript metadata
        transcript_meta = None
        for transcript in transcripts_index:
            if transcript.get('video_path') == video_path:
                transcript_meta = transcript
                break

        video_info = {
            "filename": video_filename,
            "path": f"/videos/{video_filename}",
            "size": file_stats.st_size,
            "created": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
            "modified": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            "transcript_id": transcript_meta.get('id') if transcript_meta else None,
            "has_transcript": transcript_meta is not None,
            "transcript_meta": transcript_meta
        }

        return video_info
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error getting video info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving video info: {str(e)}")

# Optional: Example of how to add more advanced features
@app.post("/chat_with_fallback")
async def chat_with_fallback(request: ChatRequest):
    try:
        # Primary service call
        return await primary_chat(request)
    except HTTPException:
        # Fallback logic (you could implement alternative services or local responses)
        return {
            "response": "I'm sorry, but I'm experiencing some technical difficulties. Please try again later.",
            "retrieved_contexts": [],
            "context_metadata": []
        }



if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)