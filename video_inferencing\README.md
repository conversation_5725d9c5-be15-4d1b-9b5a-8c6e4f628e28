# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it # Video Inferencing Service

This React-based service provides a user interface for video upload and transcription processing. It integrates with the backend API server to handle video files and display transcriptions.

## Project Structure

```
video_inferencing/
├── public/                # Static assets
├── src/
│   ├── App.js           # Main application component
│   ├── VideoUpload.js   # Video upload and transcription component
│   ├── components/      # UI components
│   │   └── Dropzone.js  # Drag & drop zone component
│   ├── css/            # Component-specific styles
│   └── index.js        # Application entry point
└── package.json        # Project dependencies
```

## Key Components

### 1. Video Upload Interface
- `VideoUpload.js`: Main component that:
  - Handles video file uploads
  - Manages transcription state
  - Communicates with backend API
  - Displays transcription results

### 2. Dropzone Component
- `Dropzone.js`: Reusable drag & drop component that:
  - Supports file dragging
  - Handles file selection
  - Provides visual feedback
  - Integrates with parent component

### 3. Styling
- Modern CSS styling with hover effects
- Responsive design
- Drag & drop visual feedback

## Features

### 1. Video Upload
- Drag & drop interface
- Click-to-select alternative
- Visual feedback for dragging
- File type validation

### 2. Transcription Processing
- Real-time transcription display
- Backend API integration
- Error handling
- Loading states

### 3. UI/UX
- Modern, responsive design
- Intuitive drag & drop interface
- Clear visual feedback
- Loading animations

## Dependencies

### Core Dependencies
- React
- Axios for API communication
- CSS for styling
- File handling utilities

### Development Dependencies
- React development tools
- CSS preprocessors
- Testing utilities

## Usage

### 1. Install Dependencies
```bash
npm install
```

### 2. Run Development Server
```bash
npm start
```

### 3. Using the Interface
1. Drag and drop video files into the drop zone
2. Or click to select files
3. Wait for transcription processing
4. View results in real-time

## API Integration

The service communicates with the backend API at:
- POST `/transcribe`: For video transcription

### Request Format
```javascript
const formData = new FormData();
formData.append('file', file);
```

### Response Format
```javascript
{
  transcription: string
}
```

## Development Notes

### TODO Items
- Add support for multiple video files
- Implement file list cap (suggested: 5 files)
- Add file removal functionality
- Improve error handling
- Add file validation

### Best Practices
- Use controlled components for file handling
- Implement proper error boundaries
- Add loading states
- Validate file types and sizes

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

Please refer to the LICENSE file for details.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
