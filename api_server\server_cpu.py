# This is a CPU-only version of server.py
# Copy this content to server.py or create a separate file for CPU mode

# At the beginning of server.py, modify these lines:

# Original lines (around line 22-47):
# tts = TTS("tts_models/en/vctk/vits").to("cuda")
# device = "cuda"
# compute_type = "float16"

# Replace with:
import torch
device = "cpu" if not torch.cuda.is_available() else "cuda"
compute_type = "float32" if device == "cpu" else "float16"

# For TTS initialization:
if device == "cuda":
    tts = TTS("tts_models/en/vctk/vits").to("cuda")
else:
    tts = TTS("tts_models/en/vctk/vits").to("cpu")

# The rest of the server.py file remains the same
# This ensures the application works on both CPU and GPU systems
