# Video Processing Service

This service handles video processing and FAISS-based vector storage for the voice-activated RAG application.

## Project Structure

```
my_project/
├── app.py              # Main FastAPI application
├── extract_video_data.py # Video processing utility
├── faiss_index.bin    # FAISS index file
└── frontend/         # Frontend integration
```

## Key Components

### 1. Core Service
- `app.py`: Main FastAPI application that:
  - Handles video processing
  - Manages FAISS vector storage
  - Provides API endpoints for video operations

### 2. Video Processing
- `extract_video_data.py`: Utility functions for:
  - Fetching YouTube video transcripts
  - Processing transcript data
  - Adding data to FAISS index

### 3. Data Storage
- `faiss_index.bin`: FAISS index storing video embeddings
- Uses sentence-transformers for text embeddings
- Implements efficient vector search

## Key Features

### 1. Video Processing
- YouTube video transcript extraction
- Transcript text processing
- Embedding generation

### 2. Vector Storage
- FAISS-based vector storage
- Efficient similarity search
- Metadata management

### 3. API Endpoints
- `/add/`: Add video transcript to index
- Uses SentenceTransformer for embeddings
- Handles video metadata

## Dependencies

### Core Dependencies
- FastAPI
- Pydantic
- FAISS
- SentenceTransformers
- YouTubeTranscriptApi
- NumPy
- Pickle

## Usage

### 1. Add Video to Index
```python
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()

class VideoRequest(BaseModel):
    video_id: str

@app.post("/add/")
def add_video(video: VideoRequest):
    # Process video and add to FAISS index
    pass
```

### 2. Query Video Data
```python
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()

class QueryRequest(BaseModel):
    query: str

@app.post("/query/")
def query_video_data(query: QueryRequest):
    # Perform similarity search using FAISS
    pass
```

## Configuration

### Environment Variables
- `FAISS_INDEX_PATH`: Path to FAISS index file
- `VIDEO_METADATA_PATH`: Path to video metadata storage

### Model Configuration
- Uses `sentence-transformers/all-MiniLM-L6-v2` for embeddings
- FAISS index dimension: 384
- Implements FlatL2 index for similarity search

## Development

### Building the Service
```bash
# Install dependencies
pip install fastapi uvicorn faiss-cpu sentence-transformers youtube-transcript-api

# Run the service
uvicorn app:app --reload
```

### Testing
```bash
# Test adding a video
curl -X POST "http://localhost:8000/add/" \
  -H "Content-Type: application/json" \
  -d '{"video_id": "VIDEO_ID"}'

# Test querying
curl -X POST "http://localhost:8000/query/" \
  -H "Content-Type: application/json" \
  -d '{"query": "SEARCH_QUERY"}'
```

## Integration

This service integrates with:
- Backend API server for video processing
- Frontend application for user interface
- FAISS for efficient vector storage

## License

Please refer to the LICENSE file for details.
