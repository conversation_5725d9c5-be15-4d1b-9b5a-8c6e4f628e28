from fastapi import FastAPI, HTTPException
import json
import math
import shutil
import faiss
import os
from typing import List

from pydantic import BaseModel
from groq import Groq
from dotenv import load_dotenv

from llama_index.core import (
    VectorStoreIndex,
    StorageContext,
    load_index_from_storage,
    Settings,
    Document,
)
from llama_index.vector_stores.faiss import FaissVectorStore
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.core.node_parser import SentenceSplitter

# --- Global Settings & Initialization ---

load_dotenv()

def format_timestamp(seconds):
    """Format seconds into MM:SS.mmm format for display"""
    if seconds is None:
        return "00:00.000"
    minutes = math.floor(seconds / 60)
    remaining_seconds = seconds % 60
    milliseconds = int((remaining_seconds - math.floor(remaining_seconds)) * 1000)
    return f"{minutes:02d}:{math.floor(remaining_seconds):02d}.{milliseconds:03d}"

app = FastAPI()

# LlamaIndex settings
Settings.embed_model = HuggingFaceEmbedding(model_name="all-MiniLM-L6-v2")
Settings.node_parser = SentenceSplitter(chunk_size=512, chunk_overlap=20)
dim = 384  # Embedding dimension for "all-MiniLM-L6-v2"

# Directories for storing data
index_dir = "embeddings"
merged_dir = "merged_embeddings"
chat_mapping_dir = "chat_mappings"
os.makedirs(index_dir, exist_ok=True)
os.makedirs(merged_dir, exist_ok=True)
os.makedirs(chat_mapping_dir, exist_ok=True)

# Load or initialize chat-to-videos mapping
chat_mapping_file = os.path.join(chat_mapping_dir, "chat_to_videos.json")
try:
    with open(chat_mapping_file, 'r') as f:
        chat_to_videos = json.load(f)
except (FileNotFoundError, json.JSONDecodeError):
    chat_to_videos = {}

# --- Pydantic Models for API Requests ---

class FilePathRequest(BaseModel):
    file_path: str
    context_id: str = "default"
    title: str = ""

class ChatRequest(BaseModel):
    query: str
    top_k: int = 3
    model: str = "llama-3.1-8b-instant"
    context_id: str = "default"

class MergeRequest(BaseModel):
    chat_id: str
    video_ids: List[str]

# --- API Endpoints ---

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

@app.get("/available_embeddings")
async def get_available_embeddings():
    """Get a list of all available video embeddings."""
    try:
        # Get all files in the embeddings directory
        all_files = os.listdir("./embeddings")

        # Extract base names without extensions and filter out duplicates
        embedding_names = set()
        for filename in all_files:
            # Skip any system files, hidden files, or metadata files
            if filename.startswith('.') or filename.startswith('__') or filename.endswith('_metadata.pkl'):
                continue

            # Only include .index files
            base_name = os.path.splitext(filename)[0]
            if base_name:
                # We're now using base filenames without timestamps
                embedding_names.add(base_name)

        # Convert to sorted list
        sorted_names = sorted(list(embedding_names))

        return {"embeddings": sorted_names}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving available embeddings: {str(e)}"
        )

@app.post("/embed")
async def embed_texts(request: FilePathRequest):
    file_path, context_id = request.file_path, request.context_id

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            json_data = json.load(f)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")    # Extract video_id from file path (assuming the file is named after the video)

    video_id = os.path.basename(os.path.dirname(file_path)).rsplit('.', 1)[0]
    print("VIDEO ID: ", video_id)

    documents = []
    for item in json_data:
        text = item.get("text") or item.get("content") or item.get("description")
        if text:
            metadata = {
                "start_time": item.get("start_time"),
                "end_time": item.get("end_time"),
                "file_path": os.path.basename(file_path),
                "video_id": video_id,
            }
            documents.append(Document(text=text, metadata=metadata))

    if not documents:
        raise HTTPException(status_code=400, detail="No valid text content found in JSON")

    persist_path = os.path.join(index_dir, context_id)
    
    try:
        if os.path.exists(persist_path):
            vector_store = FaissVectorStore.from_persist_dir(persist_path)
            storage_context = StorageContext.from_defaults(vector_store=vector_store, persist_dir=persist_path)
            index = load_index_from_storage(storage_context)
            index.insert(documents)
            message = f"Successfully updated embeddings for context '{context_id}'."
        else:
            faiss_index = faiss.IndexFlatL2(dim)
            vector_store = FaissVectorStore(faiss_index=faiss_index)
            storage_context = StorageContext.from_defaults(vector_store=vector_store)
            index = VectorStoreIndex.from_documents(documents, storage_context=storage_context)
            message = f"Successfully created embeddings for context '{context_id}'."
        
        index.storage_context.persist(persist_dir=persist_path)
        total_docs = len(index.docstore.docs)

        # Also merge with "all" embeddings
        all_persist_path = os.path.join(merged_dir, "all")
        try:
            if os.path.exists(all_persist_path):
                # Load existing "all" index and add new documents
                all_vector_store = FaissVectorStore.from_persist_dir(all_persist_path)
                all_storage_context = StorageContext.from_defaults(vector_store=all_vector_store, persist_dir=all_persist_path)
                all_index = load_index_from_storage(all_storage_context)
                all_index.insert_documents(documents)
            else:
                # Create new "all" index
                all_faiss_index = faiss.IndexFlatL2(dim)
                all_vector_store = FaissVectorStore(faiss_index=all_faiss_index)
                all_storage_context = StorageContext.from_defaults(vector_store=all_vector_store)
                all_index = VectorStoreIndex.from_documents(documents, storage_context=all_storage_context)
            
            all_index.storage_context.persist(persist_dir=all_persist_path)
        except Exception as e:
            print(f"Warning: Failed to update 'all' embeddings: {e}")

        return {"message": message, "total_documents": total_docs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing embeddings: {e}")

@app.get("/get_available_embeddings")
async def get_available_embeddings():
    """Get a list of all available video and chat embeddings."""
    video_ids = [d for d in os.listdir(index_dir) if os.path.isdir(os.path.join(index_dir, d))] if os.path.exists(index_dir) else []
    chat_ids = [d for d in os.listdir(merged_dir) if os.path.isdir(os.path.join(merged_dir, d))] if os.path.exists(merged_dir) else []
    return {"video_ids": sorted(video_ids), "chat_ids": sorted(chat_ids)}

@app.post("/merge_embeddings")
async def merge_embeddings(request: MergeRequest):
    chat_id, video_ids = request.chat_id, request.video_ids

    if("ALL" in video_ids or  "GENERAL" in video_ids):
        return {
            "message": f"No Merging",
            "total_texts": 1
        }
    
    if not video_ids:
        raise HTTPException(status_code=400, detail="No video IDs provided for merging.")

    all_nodes = []
    for video_id in video_ids:
        video_index_path = os.path.join(index_dir, video_id)
        if not os.path.exists(video_index_path):
            print(f"Warning: Index for video_id '{video_id}' not found. Skipping.")
            continue
        try:
            # Use FAISS-specific loading method
            vector_store = FaissVectorStore.from_persist_dir(video_index_path)
            storage_context = StorageContext.from_defaults(vector_store=vector_store, persist_dir=video_index_path)
            index = load_index_from_storage(storage_context)
            all_nodes.extend(index.docstore.docs.values())
        except Exception as e:
            print(f"Warning: Failed to load index for video_id '{video_id}': {e}")

    if not all_nodes:
        raise HTTPException(status_code=400, detail="No valid embeddings found for the given video_ids.")

    merged_index_path = os.path.join(merged_dir, chat_id)
    if os.path.exists(merged_index_path):
        shutil.rmtree(merged_index_path)

    faiss_index = faiss.IndexFlatL2(dim)
    vector_store = FaissVectorStore(faiss_index=faiss_index)
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    merged_index = VectorStoreIndex(nodes=all_nodes, storage_context=storage_context)
    merged_index.storage_context.persist(persist_dir=merged_index_path)

    chat_to_videos[chat_id] = video_ids
    with open(chat_mapping_file, 'w') as f:
        json.dump(chat_to_videos, f, indent=4)

    return {
        "message": f"Successfully merged {len(video_ids)} videos into chat ID '{chat_id}'.",
        "total_texts": len(all_nodes)
    }

@app.post("/chat")
async def chat_with_context(request: ChatRequest):
    context_id, query, top_k = request.context_id, request.query, request.top_k

    if context_id == "GENERAL":
        retrieved_nodes = []
    elif context_id == "ALL":
        all_persist_path = os.path.join(merged_dir, "all")
        
        if not os.path.exists(all_persist_path):
            raise HTTPException(status_code=404, detail="ALL context not found. No embeddings have been processed yet.")
        
        try:
            vector_store = FaissVectorStore.from_persist_dir(all_persist_path)
            storage_context = StorageContext.from_defaults(vector_store=vector_store, persist_dir=all_persist_path)
            index = load_index_from_storage(storage_context)
            retriever = index.as_retriever(similarity_top_k=top_k)
            retrieved_nodes = retriever.retrieve(query)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to retrieve ALL context: {e}")
    else:
        base_dir = merged_dir if context_id in chat_to_videos else index_dir
        persist_path = os.path.join(base_dir, context_id)

        if not os.path.exists(persist_path):
            raise HTTPException(status_code=404, detail=f"Context '{context_id}' not found.")
        
        try:
            vector_store = FaissVectorStore.from_persist_dir(persist_path)
            storage_context = StorageContext.from_defaults(vector_store=vector_store, persist_dir=persist_path)
            index = load_index_from_storage(storage_context)
            retriever = index.as_retriever(similarity_top_k=top_k)
            retrieved_nodes = retriever.retrieve(query)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to retrieve context: {e}")

    if not retrieved_nodes:
        context = "No relevant context found."
    else:
        context_parts = []
        for node in retrieved_nodes:
            meta = node.metadata
            text = node.get_content()
            if meta.get("start_time") is not None and meta.get("end_time") is not None:
                start = format_timestamp(meta["start_time"])
                end = format_timestamp(meta["end_time"])
                context_parts.append(f'[{start} - {end}] {text}')
            else:
                context_parts.append(text)
            if meta.get("video_id") is not None:
                video_id = meta["video_id"]
                context_parts.append(f'video_file name: {video_id}')
        context = "\n\n".join(context_parts)

    prompt = f"""Context:
{context}

Query: {query}

Provide a clear, concise, and direct answer to the query based strictly on the given context. Focus on extracting the key information and avoid unnecessary elaboration. If the context includes timestamps, you can refer to specific parts of the content by their time markers."""

    try:
        client = Groq(api_key="********************************************************")
        response = client.chat.completions.create(
            model=request.model or "llama3-8b-8192",
            messages=[
                {
                    'role': 'system',
                    'content': 'You are a precise AI assistant. Extract and summarize key information directly from the given context. Do not provide the context alone. Be concise and factual. Don\'t mention about the context to the user, just use it. Keep the length of the response short.'
                },
                {'role': 'user', 'content': prompt}
            ],
            temperature=0.3,
            max_tokens=500
        )
        cleaned_response = response.choices[0].message.content.strip()

        context_with_metadata = [
            {"text": node.get_content(), "metadata": node.metadata}
            for node in retrieved_nodes
        ]
        print("HI")
        print(node.metadata.video_id for node in retrieved_nodes)
        print("HI")
        print(node.metadata.start_time for node in retrieved_nodes)
        print("HI")
        print(node.metadata.end_time for node in retrieved_nodes)

        return {
            "response": cleaned_response,
            "retrieved_contexts": [c["text"] for c in context_with_metadata],
            "context_metadata": context_with_metadata
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error with Groq API: {e}")