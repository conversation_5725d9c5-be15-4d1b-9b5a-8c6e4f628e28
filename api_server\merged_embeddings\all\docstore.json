{"docstore/metadata": {"64904dc7-ffc6-4176-b64a-64f37d36015c": {"doc_hash": "a9fa4d0d1bce2f1dc8a516b5936fb9ab0d83d7cbc7a90ac6f508d494fa68f0fc"}, "ceff159b-7a59-45ce-930a-b7c2406056b4": {"doc_hash": "336390f869673fc93caf19d31ae008ef60097c19023e885e7c7405ccbf9b94ff"}, "ccb7c153-aac0-4f5d-9fab-c3008d172aef": {"doc_hash": "7e57206fe95b3731b94c0a71265ce769132ce4c9e113eb0c7e5bcfada26a47d6"}, "5d14a81b-912d-43db-89b5-65767a645e16": {"doc_hash": "0a053eb9db12d7c321c9e7e26d05d4171504e708cfe6ac7c93b06793295b2825"}, "9c33a253-8e22-49f6-a5a2-7b1e3ee11693": {"doc_hash": "2fe908711069d923a9ac52b9b2e378427315b24f1c874b0aa2fe2967dee6ff93"}, "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f": {"doc_hash": "480fcfc2a237e6ba77e2fff1ebabf05d4fb5ce330272919b624cf83dc5e25074"}, "0060c0ec-6d08-46a4-9e5f-d93ee3a38918": {"doc_hash": "6a2e25be111a47bb6bae3c5129e80849cda2092ffc07f5951c07f6a8da82eb8f"}, "94ad3d3c-c4a9-43a9-b53e-84b5338959d6": {"doc_hash": "830aab74de03c0828a98481cbb72deb5cbc23d2df6bcb07c7f07f9379c7c1e07"}, "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6": {"doc_hash": "c65c47d91c7d86a85ad8e8fc97834ebcb84551c0685952ccf84059faf7ec709f"}, "ee63a868-edaf-4917-b21d-39ece28a5cfe": {"doc_hash": "5fb38245dc6405b7af36ebda47abf4bafc417742a1334cb7c886cc1974ee35c4"}, "514c767c-3cf3-4211-ad03-ec27083bcbe1": {"doc_hash": "bbf925bd7428bba5ebbeb0463f2aa04e17c3d80945545021e1a77bf817ce6a1d"}, "8300d9e3-c22e-42fa-9af9-40bc12db4561": {"doc_hash": "c3f3ae6ac56de307c373f644d10a2dfdfb863aa1541a07e3724d8d0baba22cdf"}, "db55e714-59f7-460b-a5ae-1dd8ba3eb49c": {"doc_hash": "b6726b0de7a7cb65c8a31aebeea36c34e820569bab75518dc7f9ee6b58a4264e"}, "4739b662-c985-4a78-8365-3be3f81e809f": {"doc_hash": "112cf2c41b1e85acb4c3f9efa7214f83ad9c4cad253f587857bbbe21b74217c1"}, "fb105139-5e40-49b1-8d7b-ee9e319ab502": {"doc_hash": "04bef88b5f0dcaff0f1f1e55bb08395099257accad5806c999de817df90386d1"}, "c43f7f3b-0f8a-4bca-9f51-0245d03f0914": {"doc_hash": "058e4c8e74d62b468c090a559f11923b8291d2249dee5646df7668d434b49533"}, "5d402238-b90b-4b99-8961-fd787a2ec2e5": {"doc_hash": "904eb1ca5e8cbc9017747b0ffa8acc872e73a567681c9265506ba9d1e7ecd56a"}, "9bf8bbfc-97d1-4404-96af-2149fcb348f5": {"doc_hash": "6cb306b48dd708e5deb5b3abaafa45f1c4982f208e7cf7576ca213864b34f035"}, "5307fa86-e0b3-449d-bf87-44ba12e187af": {"doc_hash": "75652f5626d3a034b092bfb06b8b469a8cc8e8192c64daa9dbfa68c5bc1fd114"}, "cd545473-9fe9-4878-a453-7f72fd488cfc": {"doc_hash": "427c86b713e89ea116f9463b2a026e9bb592501861bb45c70c297ce3d7dc53dc"}, "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6": {"doc_hash": "398adcfe82df433050774ab48668f1483369eeb41352f55468c2daf162e8eefb"}, "c3eb66fd-dafa-492c-817b-b8468baaaf77": {"doc_hash": "e22c92de10c79ced1f48786f7245fcf31b574c60484e8fe401ef8b54c4ab6634"}, "b6cef169-aee3-4aa0-8668-6a0cf20e9278": {"doc_hash": "611977a7f35968dc31b638dc4ad4a8e586ab079f13855a5d4cbb0da874f60956"}, "572d898e-7822-4c53-856c-92965b485e22": {"doc_hash": "c430f211ec764bdcae76900c4e16697542b4c20511f8a6e85a9c4eb39b8909ac", "ref_doc_id": "64904dc7-ffc6-4176-b64a-64f37d36015c"}, "5d4cd244-609d-46a9-9565-179a94346806": {"doc_hash": "4ad1c6bdd83bf205fa237e96a52302ed33f8398c587da5be9a1a8ded122b2e34", "ref_doc_id": "ceff159b-7a59-45ce-930a-b7c2406056b4"}, "afe02446-e5a8-4c45-b26e-91f83027eca5": {"doc_hash": "9de25e42ac44b14202281b8dae5fe4ab91c9cd53c691e38b4d6cd11c3bd2905f", "ref_doc_id": "ccb7c153-aac0-4f5d-9fab-c3008d172aef"}, "eb9731af-f251-4d96-9141-c74ba61897b7": {"doc_hash": "1194a6613faaeb0a7a878895e81182794c6e44b2574f5abc3e763ef91df657c3", "ref_doc_id": "5d14a81b-912d-43db-89b5-65767a645e16"}, "0312a246-6ca3-4d96-a65e-102555896af9": {"doc_hash": "1266e39a5d4f42f55827c97085e7d7a15e0a65ada22276c3bcca0eef40680949", "ref_doc_id": "9c33a253-8e22-49f6-a5a2-7b1e3ee11693"}, "524549d3-6f8c-4072-ba8d-42a33c1cc8a4": {"doc_hash": "d6ef486336f72fd7c94bab14f62bae8101fbafdcb24b7ef6368225bb1ee3b1f5", "ref_doc_id": "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f"}, "1c75a087-74d8-4447-bbd6-4dc252cbd147": {"doc_hash": "1a62707754dbdba199310aa680aac7024b3e859ffee580073ab3558a663efd0e", "ref_doc_id": "0060c0ec-6d08-46a4-9e5f-d93ee3a38918"}, "a400daa0-55ac-43fb-b1fc-f0e290e9be1d": {"doc_hash": "12b72f14b15db17716a930ec2b3d2a113630ee1670a79c3ec3b960847efe2cef", "ref_doc_id": "94ad3d3c-c4a9-43a9-b53e-84b5338959d6"}, "9ed22d74-b331-4a99-a8d4-4f1e4c7c5f03": {"doc_hash": "c5e28599e9280bda0b28bd4f44b88775f9cdbc7383329d3f4616ad07cf393d28", "ref_doc_id": "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6"}, "53a51368-05a2-4a25-b91a-8ba92ac12750": {"doc_hash": "0f2bfa49416af5c8f43ec021a10b7d6f4d6034a415f7e6ecc51d1b3fc196d572", "ref_doc_id": "ee63a868-edaf-4917-b21d-39ece28a5cfe"}, "ba7f6766-4281-490a-8201-580d46f6dc70": {"doc_hash": "042d0c5661055c361ac474bc0a66398f9dfec1ef8821409527ffc8f3f7085115", "ref_doc_id": "514c767c-3cf3-4211-ad03-ec27083bcbe1"}, "7cc76331-addf-47f3-8923-e8ffdd108dd7": {"doc_hash": "6c0cf1da0adc53c56ad76abafc742a8c9588ea2126b6260b89ae31f41b3e4c0c", "ref_doc_id": "8300d9e3-c22e-42fa-9af9-40bc12db4561"}, "962625cf-7f89-4c0a-98b4-927bd2220fa4": {"doc_hash": "6d60b2f115e243a490078036aa920d74db02d8d8b545de8ec301130953350590", "ref_doc_id": "db55e714-59f7-460b-a5ae-1dd8ba3eb49c"}, "8526f60d-16ce-470b-bac0-e399c902f1bc": {"doc_hash": "391414b3decc896a815cd84b2129c60099652efebfea5e52655bc5b95ffb32fe", "ref_doc_id": "4739b662-c985-4a78-8365-3be3f81e809f"}, "c21fb300-09b4-4ae2-a642-b574a0d13237": {"doc_hash": "86c0d3bfa3c69ce49d4c066fe61e575cd6ea88d278f93e74b8d014a755157049", "ref_doc_id": "fb105139-5e40-49b1-8d7b-ee9e319ab502"}, "64b184d6-fbf1-46f4-9a1c-0bf70a0970a6": {"doc_hash": "598a6095632b0b45dc8c5090d579165ed121d20727eecac8ae2dd46132c82c01", "ref_doc_id": "c43f7f3b-0f8a-4bca-9f51-0245d03f0914"}, "dafb33fb-83a1-4fd3-9bf2-8e3db62a6c8f": {"doc_hash": "361862c0f977454a9199e3e48a15071f17d11e9701fc0d58e596a2aa9b2ccf82", "ref_doc_id": "5d402238-b90b-4b99-8961-fd787a2ec2e5"}, "ab54461c-ae79-4335-9292-0151ac0da536": {"doc_hash": "4d7a1162cb93cc1ddf90f1cbaa63e493c4b99ea4340852f2366ffc3065e4dbd0", "ref_doc_id": "9bf8bbfc-97d1-4404-96af-2149fcb348f5"}, "a158490e-ca64-46f6-9639-4b3b3ddea73c": {"doc_hash": "ec88fbfdfbb20e86dbdb39d217a8df6b13bcffd567f28af97dfb2b88a33bdafe", "ref_doc_id": "5307fa86-e0b3-449d-bf87-44ba12e187af"}, "1bc671eb-4994-4efe-b3ad-ae57c9a59217": {"doc_hash": "4a7ff22f59b74ae00b39ab65c06df98010df828f0b0b4f73907cce1a5ff5e256", "ref_doc_id": "cd545473-9fe9-4878-a453-7f72fd488cfc"}, "68d50adc-f702-4ecd-8842-088faaea947b": {"doc_hash": "34f74bec1826454cdb578a2b93e65d0a6622adc986e579b2b90f9630bace0253", "ref_doc_id": "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6"}, "4f65c026-0e6d-4d45-9d9e-b802047020b0": {"doc_hash": "79f3323b887004dcd12ecf4ed33ad6b6eba116e5683145bc59984c8eb1a643f7", "ref_doc_id": "c3eb66fd-dafa-492c-817b-b8468baaaf77"}, "7fa10979-6731-4fac-93de-a54d1d1c8a1b": {"doc_hash": "60b15fc5021301e78ef4722e38e354aa6d5d85fabee94f96b2e71d1a70622fa1", "ref_doc_id": "b6cef169-aee3-4aa0-8668-6a0cf20e9278"}, "7321a728-0590-463f-b6fb-dc68c0486209": {"doc_hash": "c430f211ec764bdcae76900c4e16697542b4c20511f8a6e85a9c4eb39b8909ac", "ref_doc_id": "6c416079-407e-4fa4-900f-54a86c740742"}, "8233f550-84c5-4d0e-85ff-ceae9158a443": {"doc_hash": "4ad1c6bdd83bf205fa237e96a52302ed33f8398c587da5be9a1a8ded122b2e34", "ref_doc_id": "1c91213c-db08-41b7-8ae0-f2339c97c99a"}, "8bc770ee-60c9-493e-b49c-6ce21b5ada5b": {"doc_hash": "9de25e42ac44b14202281b8dae5fe4ab91c9cd53c691e38b4d6cd11c3bd2905f", "ref_doc_id": "9daa5958-aac1-48c6-bfb2-b8942123b14d"}, "f7731c17-e472-4c16-9529-da3620b4d0e6": {"doc_hash": "1194a6613faaeb0a7a878895e81182794c6e44b2574f5abc3e763ef91df657c3", "ref_doc_id": "8600a13c-939a-4a6b-a105-90add69b5dbb"}, "dcc6e26f-426c-4f34-92f6-88795d92bff7": {"doc_hash": "1266e39a5d4f42f55827c97085e7d7a15e0a65ada22276c3bcca0eef40680949", "ref_doc_id": "aee6e897-92e3-44fa-93c3-44eed568f9b7"}, "19f4b50f-e198-4d1e-9bd0-f2ae3e4fadac": {"doc_hash": "d6ef486336f72fd7c94bab14f62bae8101fbafdcb24b7ef6368225bb1ee3b1f5", "ref_doc_id": "75ca3661-3df4-4ca4-a540-d26f5b4d7098"}, "394b9db6-94d5-4626-8440-ae08915e1d73": {"doc_hash": "1a62707754dbdba199310aa680aac7024b3e859ffee580073ab3558a663efd0e", "ref_doc_id": "006505af-f1c0-4d87-81e9-5dcdd89173ce"}, "b6a12791-a907-46b2-9b3c-0d5440e9116c": {"doc_hash": "12b72f14b15db17716a930ec2b3d2a113630ee1670a79c3ec3b960847efe2cef", "ref_doc_id": "db95727f-89e6-4d3a-8205-3ecf4e152900"}, "b0c32b8a-00a2-4777-a981-3da67e2d0731": {"doc_hash": "c5e28599e9280bda0b28bd4f44b88775f9cdbc7383329d3f4616ad07cf393d28", "ref_doc_id": "5f739082-1605-416c-85c8-e4394574fb33"}, "3dc44c34-c210-4112-bc33-db76aa3d49e5": {"doc_hash": "0f2bfa49416af5c8f43ec021a10b7d6f4d6034a415f7e6ecc51d1b3fc196d572", "ref_doc_id": "a5ae1367-ed5c-420a-b7bb-3ebcc80cf13d"}, "1654aca6-6d13-43cb-aac0-a12a2563414b": {"doc_hash": "042d0c5661055c361ac474bc0a66398f9dfec1ef8821409527ffc8f3f7085115", "ref_doc_id": "74e4baed-24b0-4770-898e-2cb2471e1dc1"}, "32def915-32ad-42e4-a653-645cf75b419e": {"doc_hash": "6c0cf1da0adc53c56ad76abafc742a8c9588ea2126b6260b89ae31f41b3e4c0c", "ref_doc_id": "8e7819d6-14c5-4259-ab94-2ae78e4f6534"}, "083531dd-04f0-4e93-9b35-cdbae9f5e4d8": {"doc_hash": "6d60b2f115e243a490078036aa920d74db02d8d8b545de8ec301130953350590", "ref_doc_id": "92108841-daef-45b2-81f9-b90b043534f8"}, "6df07390-7053-4693-bf3c-12a276cf58f3": {"doc_hash": "391414b3decc896a815cd84b2129c60099652efebfea5e52655bc5b95ffb32fe", "ref_doc_id": "89cad037-3cd0-40c1-a13d-48954b152a50"}, "2b49e0ea-8faf-4967-84c5-545c35825f3d": {"doc_hash": "86c0d3bfa3c69ce49d4c066fe61e575cd6ea88d278f93e74b8d014a755157049", "ref_doc_id": "84eb41fb-4b99-42fb-8263-6b6773e745e7"}, "1c9e2271-0f56-4ba5-8691-afaf0954aab9": {"doc_hash": "598a6095632b0b45dc8c5090d579165ed121d20727eecac8ae2dd46132c82c01", "ref_doc_id": "d944846f-285b-4641-8524-1486d47b3bb7"}, "5f25826d-2dfd-4b55-837e-0d8bbd8028d4": {"doc_hash": "361862c0f977454a9199e3e48a15071f17d11e9701fc0d58e596a2aa9b2ccf82", "ref_doc_id": "34a2394e-a459-45d5-ba17-748ca422dd7e"}, "9358c619-5676-4724-99af-e72a5bd80b53": {"doc_hash": "4d7a1162cb93cc1ddf90f1cbaa63e493c4b99ea4340852f2366ffc3065e4dbd0", "ref_doc_id": "b1e61df7-4462-4b84-a4ed-8c78ef71a548"}, "c6e2a2ea-3bf6-48f9-879c-d60bc1e4c0b7": {"doc_hash": "ec88fbfdfbb20e86dbdb39d217a8df6b13bcffd567f28af97dfb2b88a33bdafe", "ref_doc_id": "734ac39c-3d14-4486-a55b-0f3b3ca8197b"}, "57539145-be03-4bd8-b252-63dbc5d1c57e": {"doc_hash": "4a7ff22f59b74ae00b39ab65c06df98010df828f0b0b4f73907cce1a5ff5e256", "ref_doc_id": "4e7531b9-84ba-4055-8863-8f9a285a4613"}, "805bc548-6810-46e2-ac54-8de2729c725c": {"doc_hash": "34f74bec1826454cdb578a2b93e65d0a6622adc986e579b2b90f9630bace0253", "ref_doc_id": "6d053aa8-8e93-4ec6-a42d-ec95a03408cc"}, "b98d124d-0b57-4ba1-b7ab-e48b1636d89d": {"doc_hash": "79f3323b887004dcd12ecf4ed33ad6b6eba116e5683145bc59984c8eb1a643f7", "ref_doc_id": "c6cc0bff-ef7d-4a73-86c2-befb834c0fe5"}, "76a939d3-c1c4-4f5e-872b-313303cf8093": {"doc_hash": "60b15fc5021301e78ef4722e38e354aa6d5d85fabee94f96b2e71d1a70622fa1", "ref_doc_id": "f13c080a-aadc-46fe-94b9-c91022f2fecb"}, "1870ccef-95f4-4833-94cf-bf6d22d049ff": {"doc_hash": "d475aafd82f96f4e811f61f31adea8c0afb6e991f6b5eb9e4fc6a2ebda0740ed", "ref_doc_id": "aa9d7295-e5e3-44e9-b291-50dd6dc446c6"}, "a627dd3e-b472-44d0-9d65-e5bd1faa6342": {"doc_hash": "e1c18e81199230d07e93600cc01651c3e6ef104d3356ab6cf9a99946ce070a35", "ref_doc_id": "5f981593-c5ea-4493-afea-26ecf87916bd"}, "25bf9469-5c7c-4a38-ba81-fbcd400656aa": {"doc_hash": "00a420052609194d99851d38f349d959b655c136247b95336d89d4b700cdc1f2", "ref_doc_id": "520bde88-e758-4862-8835-f3ddeab82473"}, "4f506000-4b94-4eaa-8233-1c74a1544cbd": {"doc_hash": "c3a799a548dd17640c2e61fba7cf53cace222b3a3597ee079e95cc7f54437e4f", "ref_doc_id": "16b12a71-b70e-4b93-aa74-79d5f7409815"}, "b7265ee0-0789-48ea-8ef3-9820939dd4fa": {"doc_hash": "aada3589375ab096d5d3440342e2de14fa6c5f7b40de88b92faae6405f0aefd6", "ref_doc_id": "e97b9166-b365-43c1-8bfd-17860579f884"}, "7409083e-d8c6-4247-9653-a97e88bed66b": {"doc_hash": "73b9b006fdd87c1dda9a23ab28db68dff31a013a51d6dfe59cdab750432ff59f", "ref_doc_id": "ee4afbae-18fd-4a33-b5d8-4cb8af2dd521"}, "c4eb940c-41e6-470c-a021-bf810a548ed4": {"doc_hash": "890e02cfdff2a9175ba7230471d6f84ccb73f931c1433bc6ce888893d3356a0d", "ref_doc_id": "34530840-ff1e-4b97-a788-c4eb8d01c037"}, "4a0415f1-0d7f-44ec-886b-ed99a02df249": {"doc_hash": "972c80e2d48397f8932a21ce32cc09e0cf9b40a3620517871523da6276840a8c", "ref_doc_id": "2aba7153-a41d-4184-aa63-0008e4133955"}, "18bc7bdb-a32f-4ace-86b9-ed0845d1ebbb": {"doc_hash": "d59123401b499ad0aff1ceea4629c2873c1ac8c7e45fb4287e178dceb7384159", "ref_doc_id": "e93c3af0-1d7b-4ff5-adf6-1a494956c1e8"}, "3ddbf740-943b-4e5b-bf9b-2a0c29cf66d3": {"doc_hash": "8306e97cdcd00644f11d38cf865be9768ec6cc5fe4e2b7d7ecc4430ef53585a6", "ref_doc_id": "bb5a72b1-e5d6-4ca4-8a66-ab30791f1d84"}, "1bb44b00-df83-4e56-946e-b8f37c6dcdea": {"doc_hash": "354aa61414d4a4b8bfbdd521621e648db784dcce6716a8401a60b71fa4ada894", "ref_doc_id": "cd7bc4a5-ed58-4cd8-a8b9-ef4cf02ec864"}, "4f79e5ae-16f7-464b-91c2-e79e767091b5": {"doc_hash": "7bb5c89dc1965b2407ed892a0ff1200bec73137a45f9da276e4ad45052f981c2", "ref_doc_id": "16f079a9-d376-47ae-ac2e-6c96ea3c5e7b"}, "72d783d4-8ffd-46bf-a5c1-b301a304cdad": {"doc_hash": "29f53781c51473c99846af4a439306ecafc6d42fd84bf28d0414e472c2106b5d", "ref_doc_id": "5097bf99-a766-4963-995d-bbcdbf3d2604"}, "0c5173fa-b1a4-4791-bad7-135a2d832bc1": {"doc_hash": "6350cdd96b6fbf2e6b76e3442aa87dccc8d09275d15b9579ac0b21ee97335035", "ref_doc_id": "871c737d-2f53-4795-b927-8771cc087290"}, "56c10654-a30e-46c5-83d1-62aa29fb096e": {"doc_hash": "b1a513c305f73b2e4ed6f88af58a0e333314454786e54bf8e59d2314a805acab", "ref_doc_id": "8b9da95a-e24a-462e-a8a9-b1deabdc680f"}, "fb6d0982-cd48-40be-ab8b-6d1400a184b5": {"doc_hash": "dab96e4cc8214a68e3d003a8cccce2e693e10de099544b5be951f89ac3eeaf55", "ref_doc_id": "f9170471-51e9-45bd-a99c-b4d9a80dc533"}, "f0f10494-e756-4fe5-9440-8ee3938e5142": {"doc_hash": "9af17455ae98f0c4ba95a28fead136bc075fefc2cc7aa78c6e994ea9b19813ab", "ref_doc_id": "081b62b7-c77c-4de1-a0c5-d4b4a607f2c9"}, "7f6fa760-098f-4d38-bb21-f74e23414e4a": {"doc_hash": "5010824b2bb5ad208b022a85f503a1e45ded181ce6d3df6c921267d25d796039", "ref_doc_id": "fd2d6cd7-dcbc-49d8-8d3a-aff857b3e30f"}, "ee813a97-ad59-4902-af1b-5e230dc98696": {"doc_hash": "1fc437b935e0b51e2d1eb420d58f81cf1830d84d15d34e8607d123a97cd23c49", "ref_doc_id": "0b070a7a-a4c0-4554-a7fd-b7102d705b65"}, "0caa6dbd-d180-4419-8184-ef74fc5993ac": {"doc_hash": "7e29742794fc49f74d4bd1ec7f35835e8807f6a63d3f2b4de666b1c65ad96a5e", "ref_doc_id": "9fd09855-435a-44d5-a61d-61d5662819a6"}, "39e39144-9320-47df-affc-ca4e5bfa8202": {"doc_hash": "6fc618ca9a968ff7243c17939759940eef32922dbab83b7de59b3f1d6458f547", "ref_doc_id": "0f734f30-9135-4eae-b5c8-3d9b74c3b2ff"}, "07eb30d8-a116-4278-aa01-b765a7a92715": {"doc_hash": "edb0d78b7a70ab4fea6d55307a6a92d905e957135f223327824876dbe0c21131", "ref_doc_id": "8ca51b17-ece2-4bf4-ac37-c36ee91ea04c"}, "8396927f-4a06-4f7f-a5df-b6fd8e022bac": {"doc_hash": "775374fd34a02993cfd4bc9c0e3748fa61d2c05fa654789f264b86edb0409151", "ref_doc_id": "f059ae34-2f2d-4157-ad3f-8f33373183e1"}, "d24e1bab-cb66-482c-902a-06327977c2f2": {"doc_hash": "ef29a74bf827c8b537f972177469f8c91bd7c1b8e0803031115e27b0d1a07113", "ref_doc_id": "586c9e35-a1cc-4310-a90d-c38e4c541bf1"}, "d92e2f63-7bf1-4ed8-84eb-fd9dfb3aecca": {"doc_hash": "48bcdf8385395757a0a49b92d9669aa95b98c380d2763d78465fb4b5b1a127a7", "ref_doc_id": "d629396f-04c1-4e41-b1a9-f981897ef1f4"}, "4c320d59-f30f-463c-a80a-8a3bb292e45b": {"doc_hash": "f3d9fca8aa25fe2e8e4075216f3b4c2cf24f304fd6bc3b6dba18002d1b7d45aa", "ref_doc_id": "38b86218-6666-4ee8-a536-1aaf6edea449"}, "6eef3099-8d6e-4c6c-a08b-269e043f62d9": {"doc_hash": "a1e7dda825987040c9ccba55c682fa0d1e6dc011cc24bbf9fccba2d0476211bf", "ref_doc_id": "f57d1161-1c8b-449c-ae99-e75172ac83cd"}, "6cab307a-7cd3-407d-ad3c-ea107bcffff6": {"doc_hash": "f63618686b75a1d73ed0d27801fd7d1a26c1541d0f6eaf3e57768fba1c5cf269", "ref_doc_id": "da0e8d8f-a4eb-45aa-a8ed-4fc00a6a56d8"}, "3f52b115-84fb-4049-a71d-62d010b3bcd9": {"doc_hash": "3496eceff30d7bdbcc7a9f371dbae0f1cf61e4ca43d4c3ea62ca7b1098752ef6", "ref_doc_id": "9e404a5f-49e5-49e2-a63b-4c88f79acc40"}, "833cd018-0f09-420f-9dc1-a03bf6d24a2e": {"doc_hash": "2707fa8c0e4c929de36ff60820a052ba3f028e75debf7e39c560f65722d32d64", "ref_doc_id": "1d205454-5011-4033-aeea-52c2cfc6ad25"}, "e2a01810-4742-4238-a8e3-b847b0de33b1": {"doc_hash": "48ebf53b38dea79562097ce985849e9577ffd1783bf811e1af24be790e375b08", "ref_doc_id": "dfcb403f-b5e4-4e78-a110-23f7c23674c3"}, "2dd0f6ea-90e1-4375-a062-55c6b0483a93": {"doc_hash": "11ec1d2f2ed78f930b3cb4415facd121b7e2c63100269ec93a6f9ef6704bb5f7", "ref_doc_id": "9ea26817-f414-45dd-bc5e-ed6330341c66"}, "5864f7d0-8eec-458b-8653-73345a870947": {"doc_hash": "b2e915289df40f960c900b621efc6534e0b14768c6eb92979edfbfe111347747", "ref_doc_id": "ee561a68-171d-4db9-ba73-397cb3b67f1b"}, "66594042-e133-4306-934e-4abe812eaeae": {"doc_hash": "8d7b7659c6865fb5ee10b92953e9b2e8c2f753a92003f6d12796a4204530e629", "ref_doc_id": "b4c888a3-5542-41b4-abbe-36138ca53be5"}, "08cd5e5d-d0d0-423d-804a-5595f53746f0": {"doc_hash": "10dc7b430ee69853ba3be08dcc636ed0e24785e294ce69534e8790eec39059b8", "ref_doc_id": "2467e199-f6b1-4c06-bdf4-7fec0e28779d"}, "295311eb-8646-426b-ad28-140d090fc86a": {"doc_hash": "ab9c592ca0219afa4376a86f601372336c7472e98792711507545d39afc65cd0", "ref_doc_id": "39fe741f-1a41-4544-9113-7b4542313e1a"}, "9afa3243-4b4e-4531-bf88-f2632287aa22": {"doc_hash": "6a34b16e9dbbfb04754985485187d1cce16ccd1d60c646f70c399c549a5d4bda", "ref_doc_id": "b3cbe17c-d42e-42ac-93ed-1b5607e2d5ef"}, "7a92c0a3-696d-4bc0-b09b-b6d290097af2": {"doc_hash": "9f2a75b4c522d5e292bf6b952b0b96109b6fa415e305d54184226e747b3d912b", "ref_doc_id": "7e533cfa-8325-4892-9031-fc67f332e9bd"}, "2a1bb8b3-895a-4c83-a653-ddc4cb1f27a4": {"doc_hash": "d79b21e6e91a8efbc18e8ecfea4b3a0d953e43b060961dfdcdb3702f0e31a9d2", "ref_doc_id": "0ef5a9ec-83c6-4e95-addd-26a3b58e1935"}, "35d911c0-d00c-404f-abee-09dc1120f616": {"doc_hash": "ebe625f88483692400f084c1346e8aeef53d674c12dfc9d06f83e4f3bd1d0ff3", "ref_doc_id": "99760400-57bd-4e01-b5ce-e44264ff8ab2"}, "8d6707d4-83ae-4676-9440-7c7d8d0bdb60": {"doc_hash": "35cd874a647137e944be555018ad562ef88f94e2ae1375f945fc95b82b3383c3", "ref_doc_id": "6cc45592-fb25-42a3-b94b-ca5a547b0ecd"}, "aeaf360b-cc08-4e9a-a331-98e379b0b666": {"doc_hash": "21fd7b05d70d4555bd6864528d99b38bef9a1f9257b18e237ef07507e65c6701", "ref_doc_id": "b65ac3c3-dd43-49e7-b562-2cc9f35869d9"}, "4a2d0a6a-8a60-45ca-8533-4c796493cc78": {"doc_hash": "4707393ed7cb7862a92c50fb762b17ff7c84c3499988a7bab8fbbec2706ea436", "ref_doc_id": "e8718ede-0ca4-430f-a642-71818a7e12a0"}}, "docstore/ref_doc_info": {"64904dc7-ffc6-4176-b64a-64f37d36015c": {"node_ids": ["572d898e-7822-4c53-856c-92965b485e22"], "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}}, "ceff159b-7a59-45ce-930a-b7c2406056b4": {"node_ids": ["5d4cd244-609d-46a9-9565-179a94346806"], "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}}, "ccb7c153-aac0-4f5d-9fab-c3008d172aef": {"node_ids": ["afe02446-e5a8-4c45-b26e-91f83027eca5"], "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}}, "5d14a81b-912d-43db-89b5-65767a645e16": {"node_ids": ["eb9731af-f251-4d96-9141-c74ba61897b7"], "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}}, "9c33a253-8e22-49f6-a5a2-7b1e3ee11693": {"node_ids": ["0312a246-6ca3-4d96-a65e-102555896af9"], "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}}, "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f": {"node_ids": ["524549d3-6f8c-4072-ba8d-42a33c1cc8a4"], "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}}, "0060c0ec-6d08-46a4-9e5f-d93ee3a38918": {"node_ids": ["1c75a087-74d8-4447-bbd6-4dc252cbd147"], "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}}, "94ad3d3c-c4a9-43a9-b53e-84b5338959d6": {"node_ids": ["a400daa0-55ac-43fb-b1fc-f0e290e9be1d"], "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}}, "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6": {"node_ids": ["9ed22d74-b331-4a99-a8d4-4f1e4c7c5f03"], "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}}, "ee63a868-edaf-4917-b21d-39ece28a5cfe": {"node_ids": ["53a51368-05a2-4a25-b91a-8ba92ac12750"], "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}}, "514c767c-3cf3-4211-ad03-ec27083bcbe1": {"node_ids": ["ba7f6766-4281-490a-8201-580d46f6dc70"], "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}}, "8300d9e3-c22e-42fa-9af9-40bc12db4561": {"node_ids": ["7cc76331-addf-47f3-8923-e8ffdd108dd7"], "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}}, "db55e714-59f7-460b-a5ae-1dd8ba3eb49c": {"node_ids": ["962625cf-7f89-4c0a-98b4-927bd2220fa4"], "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}}, "4739b662-c985-4a78-8365-3be3f81e809f": {"node_ids": ["8526f60d-16ce-470b-bac0-e399c902f1bc"], "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}}, "fb105139-5e40-49b1-8d7b-ee9e319ab502": {"node_ids": ["c21fb300-09b4-4ae2-a642-b574a0d13237"], "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}}, "c43f7f3b-0f8a-4bca-9f51-0245d03f0914": {"node_ids": ["64b184d6-fbf1-46f4-9a1c-0bf70a0970a6"], "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}}, "5d402238-b90b-4b99-8961-fd787a2ec2e5": {"node_ids": ["dafb33fb-83a1-4fd3-9bf2-8e3db62a6c8f"], "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}}, "9bf8bbfc-97d1-4404-96af-2149fcb348f5": {"node_ids": ["ab54461c-ae79-4335-9292-0151ac0da536"], "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}}, "5307fa86-e0b3-449d-bf87-44ba12e187af": {"node_ids": ["a158490e-ca64-46f6-9639-4b3b3ddea73c"], "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}}, "cd545473-9fe9-4878-a453-7f72fd488cfc": {"node_ids": ["1bc671eb-4994-4efe-b3ad-ae57c9a59217"], "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}}, "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6": {"node_ids": ["68d50adc-f702-4ecd-8842-088faaea947b"], "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}}, "c3eb66fd-dafa-492c-817b-b8468baaaf77": {"node_ids": ["4f65c026-0e6d-4d45-9d9e-b802047020b0"], "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}}, "b6cef169-aee3-4aa0-8668-6a0cf20e9278": {"node_ids": ["7fa10979-6731-4fac-93de-a54d1d1c8a1b"], "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}}, "6c416079-407e-4fa4-900f-54a86c740742": {"node_ids": ["7321a728-0590-463f-b6fb-dc68c0486209"], "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}}, "1c91213c-db08-41b7-8ae0-f2339c97c99a": {"node_ids": ["8233f550-84c5-4d0e-85ff-ceae9158a443"], "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}}, "9daa5958-aac1-48c6-bfb2-b8942123b14d": {"node_ids": ["8bc770ee-60c9-493e-b49c-6ce21b5ada5b"], "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}}, "8600a13c-939a-4a6b-a105-90add69b5dbb": {"node_ids": ["f7731c17-e472-4c16-9529-da3620b4d0e6"], "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}}, "aee6e897-92e3-44fa-93c3-44eed568f9b7": {"node_ids": ["dcc6e26f-426c-4f34-92f6-88795d92bff7"], "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}}, "75ca3661-3df4-4ca4-a540-d26f5b4d7098": {"node_ids": ["19f4b50f-e198-4d1e-9bd0-f2ae3e4fadac"], "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}}, "006505af-f1c0-4d87-81e9-5dcdd89173ce": {"node_ids": ["394b9db6-94d5-4626-8440-ae08915e1d73"], "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}}, "db95727f-89e6-4d3a-8205-3ecf4e152900": {"node_ids": ["b6a12791-a907-46b2-9b3c-0d5440e9116c"], "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}}, "5f739082-1605-416c-85c8-e4394574fb33": {"node_ids": ["b0c32b8a-00a2-4777-a981-3da67e2d0731"], "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}}, "a5ae1367-ed5c-420a-b7bb-3ebcc80cf13d": {"node_ids": ["3dc44c34-c210-4112-bc33-db76aa3d49e5"], "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}}, "74e4baed-24b0-4770-898e-2cb2471e1dc1": {"node_ids": ["1654aca6-6d13-43cb-aac0-a12a2563414b"], "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}}, "8e7819d6-14c5-4259-ab94-2ae78e4f6534": {"node_ids": ["32def915-32ad-42e4-a653-645cf75b419e"], "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}}, "92108841-daef-45b2-81f9-b90b043534f8": {"node_ids": ["083531dd-04f0-4e93-9b35-cdbae9f5e4d8"], "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}}, "89cad037-3cd0-40c1-a13d-48954b152a50": {"node_ids": ["6df07390-7053-4693-bf3c-12a276cf58f3"], "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}}, "84eb41fb-4b99-42fb-8263-6b6773e745e7": {"node_ids": ["2b49e0ea-8faf-4967-84c5-545c35825f3d"], "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}}, "d944846f-285b-4641-8524-1486d47b3bb7": {"node_ids": ["1c9e2271-0f56-4ba5-8691-afaf0954aab9"], "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}}, "34a2394e-a459-45d5-ba17-748ca422dd7e": {"node_ids": ["5f25826d-2dfd-4b55-837e-0d8bbd8028d4"], "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}}, "b1e61df7-4462-4b84-a4ed-8c78ef71a548": {"node_ids": ["9358c619-5676-4724-99af-e72a5bd80b53"], "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}}, "734ac39c-3d14-4486-a55b-0f3b3ca8197b": {"node_ids": ["c6e2a2ea-3bf6-48f9-879c-d60bc1e4c0b7"], "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}}, "4e7531b9-84ba-4055-8863-8f9a285a4613": {"node_ids": ["57539145-be03-4bd8-b252-63dbc5d1c57e"], "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}}, "6d053aa8-8e93-4ec6-a42d-ec95a03408cc": {"node_ids": ["805bc548-6810-46e2-ac54-8de2729c725c"], "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}}, "c6cc0bff-ef7d-4a73-86c2-befb834c0fe5": {"node_ids": ["b98d124d-0b57-4ba1-b7ab-e48b1636d89d"], "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}}, "f13c080a-aadc-46fe-94b9-c91022f2fecb": {"node_ids": ["76a939d3-c1c4-4f5e-872b-313303cf8093"], "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}}, "aa9d7295-e5e3-44e9-b291-50dd6dc446c6": {"node_ids": ["1870ccef-95f4-4833-94cf-bf6d22d049ff"], "metadata": {"start_time": 0.0, "end_time": 23.2, "file_path": "transcript.json", "video_id": "maling"}}, "5f981593-c5ea-4493-afea-26ecf87916bd": {"node_ids": ["a627dd3e-b472-44d0-9d65-e5bd1faa6342"], "metadata": {"start_time": 23.64, "end_time": 62.4, "file_path": "transcript.json", "video_id": "maling"}}, "520bde88-e758-4862-8835-f3ddeab82473": {"node_ids": ["25bf9469-5c7c-4a38-ba81-fbcd400656aa"], "metadata": {"start_time": 62.8, "end_time": 98.08, "file_path": "transcript.json", "video_id": "maling"}}, "16b12a71-b70e-4b93-aa74-79d5f7409815": {"node_ids": ["4f506000-4b94-4eaa-8233-1c74a1544cbd"], "metadata": {"start_time": 98.58, "end_time": 128.5, "file_path": "transcript.json", "video_id": "maling"}}, "e97b9166-b365-43c1-8bfd-17860579f884": {"node_ids": ["b7265ee0-0789-48ea-8ef3-9820939dd4fa"], "metadata": {"start_time": 130.06, "end_time": 159.7, "file_path": "transcript.json", "video_id": "maling"}}, "ee4afbae-18fd-4a33-b5d8-4cb8af2dd521": {"node_ids": ["7409083e-d8c6-4247-9653-a97e88bed66b"], "metadata": {"start_time": 160.42, "end_time": 200.34, "file_path": "transcript.json", "video_id": "maling"}}, "34530840-ff1e-4b97-a788-c4eb8d01c037": {"node_ids": ["c4eb940c-41e6-470c-a021-bf810a548ed4"], "metadata": {"start_time": 201.1, "end_time": 235.009, "file_path": "transcript.json", "video_id": "maling"}}, "2aba7153-a41d-4184-aa63-0008e4133955": {"node_ids": ["4a0415f1-0d7f-44ec-886b-ed99a02df249"], "metadata": {"start_time": 0.0, "end_time": 18.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "e93c3af0-1d7b-4ff5-adf6-1a494956c1e8": {"node_ids": ["18bc7bdb-a32f-4ace-86b9-ed0845d1ebbb"], "metadata": {"start_time": 19.68, "end_time": 40.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "bb5a72b1-e5d6-4ca4-8a66-ab30791f1d84": {"node_ids": ["3ddbf740-943b-4e5b-bf9b-2a0c29cf66d3"], "metadata": {"start_time": 40.98, "end_time": 58.96, "file_path": "transcript.json", "video_id": "temp_brian"}}, "cd7bc4a5-ed58-4cd8-a8b9-ef4cf02ec864": {"node_ids": ["1bb44b00-df83-4e56-946e-b8f37c6dcdea"], "metadata": {"start_time": 59.5, "end_time": 72.44, "file_path": "transcript.json", "video_id": "temp_brian"}}, "16f079a9-d376-47ae-ac2e-6c96ea3c5e7b": {"node_ids": ["4f79e5ae-16f7-464b-91c2-e79e767091b5"], "metadata": {"start_time": 72.72, "end_time": 91.12, "file_path": "transcript.json", "video_id": "temp_brian"}}, "5097bf99-a766-4963-995d-bbcdbf3d2604": {"node_ids": ["72d783d4-8ffd-46bf-a5c1-b301a304cdad"], "metadata": {"start_time": 91.34, "end_time": 115.28, "file_path": "transcript.json", "video_id": "temp_brian"}}, "871c737d-2f53-4795-b927-8771cc087290": {"node_ids": ["0c5173fa-b1a4-4791-bad7-135a2d832bc1"], "metadata": {"start_time": 115.54, "end_time": 144.2, "file_path": "transcript.json", "video_id": "temp_brian"}}, "8b9da95a-e24a-462e-a8a9-b1deabdc680f": {"node_ids": ["56c10654-a30e-46c5-83d1-62aa29fb096e"], "metadata": {"start_time": 144.34, "end_time": 170.08, "file_path": "transcript.json", "video_id": "temp_brian"}}, "f9170471-51e9-45bd-a99c-b4d9a80dc533": {"node_ids": ["fb6d0982-cd48-40be-ab8b-6d1400a184b5"], "metadata": {"start_time": 170.3, "end_time": 183.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "081b62b7-c77c-4de1-a0c5-d4b4a607f2c9": {"node_ids": ["f0f10494-e756-4fe5-9440-8ee3938e5142"], "metadata": {"start_time": 183.88, "end_time": 211.4, "file_path": "transcript.json", "video_id": "temp_brian"}}, "fd2d6cd7-dcbc-49d8-8d3a-aff857b3e30f": {"node_ids": ["7f6fa760-098f-4d38-bb21-f74e23414e4a"], "metadata": {"start_time": 211.02, "end_time": 239.8, "file_path": "transcript.json", "video_id": "temp_brian"}}, "0b070a7a-a4c0-4554-a7fd-b7102d705b65": {"node_ids": ["ee813a97-ad59-4902-af1b-5e230dc98696"], "metadata": {"start_time": 239.46, "end_time": 264.56, "file_path": "transcript.json", "video_id": "temp_brian"}}, "9fd09855-435a-44d5-a61d-61d5662819a6": {"node_ids": ["0caa6dbd-d180-4419-8184-ef74fc5993ac"], "metadata": {"start_time": 264.78, "end_time": 291.66, "file_path": "transcript.json", "video_id": "temp_brian"}}, "0f734f30-9135-4eae-b5c8-3d9b74c3b2ff": {"node_ids": ["39e39144-9320-47df-affc-ca4e5bfa8202"], "metadata": {"start_time": 291.86, "end_time": 314.34, "file_path": "transcript.json", "video_id": "temp_brian"}}, "8ca51b17-ece2-4bf4-ac37-c36ee91ea04c": {"node_ids": ["07eb30d8-a116-4278-aa01-b765a7a92715"], "metadata": {"start_time": 314.6, "end_time": 332.06, "file_path": "transcript.json", "video_id": "temp_brian"}}, "f059ae34-2f2d-4157-ad3f-8f33373183e1": {"node_ids": ["8396927f-4a06-4f7f-a5df-b6fd8e022bac"], "metadata": {"start_time": 332.5, "end_time": 360.28, "file_path": "transcript.json", "video_id": "temp_brian"}}, "586c9e35-a1cc-4310-a90d-c38e4c541bf1": {"node_ids": ["d24e1bab-cb66-482c-902a-06327977c2f2"], "metadata": {"start_time": 360.14, "end_time": 366.98, "file_path": "transcript.json", "video_id": "temp_brian"}}, "d629396f-04c1-4e41-b1a9-f981897ef1f4": {"node_ids": ["d92e2f63-7bf1-4ed8-84eb-fd9dfb3aecca"], "metadata": {"start_time": 367.2, "end_time": 395.4, "file_path": "transcript.json", "video_id": "temp_brian"}}, "38b86218-6666-4ee8-a536-1aaf6edea449": {"node_ids": ["4c320d59-f30f-463c-a80a-8a3bb292e45b"], "metadata": {"start_time": 395.72, "end_time": 422.6, "file_path": "transcript.json", "video_id": "temp_brian"}}, "f57d1161-1c8b-449c-ae99-e75172ac83cd": {"node_ids": ["6eef3099-8d6e-4c6c-a08b-269e043f62d9"], "metadata": {"start_time": 423.04, "end_time": 442.26, "file_path": "transcript.json", "video_id": "temp_brian"}}, "da0e8d8f-a4eb-45aa-a8ed-4fc00a6a56d8": {"node_ids": ["6cab307a-7cd3-407d-ad3c-ea107bcffff6"], "metadata": {"start_time": 442.64, "end_time": 471.1, "file_path": "transcript.json", "video_id": "temp_brian"}}, "9e404a5f-49e5-49e2-a63b-4c88f79acc40": {"node_ids": ["3f52b115-84fb-4049-a71d-62d010b3bcd9"], "metadata": {"start_time": 471.32, "end_time": 497.38, "file_path": "transcript.json", "video_id": "temp_brian"}}, "1d205454-5011-4033-aeea-52c2cfc6ad25": {"node_ids": ["833cd018-0f09-420f-9dc1-a03bf6d24a2e"], "metadata": {"start_time": 497.84, "end_time": 515.22, "file_path": "transcript.json", "video_id": "temp_brian"}}, "dfcb403f-b5e4-4e78-a110-23f7c23674c3": {"node_ids": ["e2a01810-4742-4238-a8e3-b847b0de33b1"], "metadata": {"start_time": 515.44, "end_time": 537.56, "file_path": "transcript.json", "video_id": "temp_brian"}}, "9ea26817-f414-45dd-bc5e-ed6330341c66": {"node_ids": ["2dd0f6ea-90e1-4375-a062-55c6b0483a93"], "metadata": {"start_time": 537.52, "end_time": 546.44, "file_path": "transcript.json", "video_id": "temp_brian"}}, "ee561a68-171d-4db9-ba73-397cb3b67f1b": {"node_ids": ["5864f7d0-8eec-458b-8653-73345a870947"], "metadata": {"start_time": 546.94, "end_time": 572.84, "file_path": "transcript.json", "video_id": "temp_brian"}}, "b4c888a3-5542-41b4-abbe-36138ca53be5": {"node_ids": ["66594042-e133-4306-934e-4abe812eaeae"], "metadata": {"start_time": 573.06, "end_time": 601.88, "file_path": "transcript.json", "video_id": "temp_brian"}}, "2467e199-f6b1-4c06-bdf4-7fec0e28779d": {"node_ids": ["08cd5e5d-d0d0-423d-804a-5595f53746f0"], "metadata": {"start_time": 602.12, "end_time": 629.9, "file_path": "transcript.json", "video_id": "temp_brian"}}, "39fe741f-1a41-4544-9113-7b4542313e1a": {"node_ids": ["295311eb-8646-426b-ad28-140d090fc86a"], "metadata": {"start_time": 630.56, "end_time": 655.04, "file_path": "transcript.json", "video_id": "temp_brian"}}, "b3cbe17c-d42e-42ac-93ed-1b5607e2d5ef": {"node_ids": ["9afa3243-4b4e-4531-bf88-f2632287aa22"], "metadata": {"start_time": 0.54, "end_time": 26.38, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "7e533cfa-8325-4892-9031-fc67f332e9bd": {"node_ids": ["7a92c0a3-696d-4bc0-b09b-b6d290097af2"], "metadata": {"start_time": 27.64, "end_time": 50.6, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "0ef5a9ec-83c6-4e95-addd-26a3b58e1935": {"node_ids": ["2a1bb8b3-895a-4c83-a653-ddc4cb1f27a4"], "metadata": {"start_time": 51.92, "end_time": 73.9, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "99760400-57bd-4e01-b5ce-e44264ff8ab2": {"node_ids": ["35d911c0-d00c-404f-abee-09dc1120f616"], "metadata": {"start_time": 74.14, "end_time": 100.16, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "6cc45592-fb25-42a3-b94b-ca5a547b0ecd": {"node_ids": ["8d6707d4-83ae-4676-9440-7c7d8d0bdb60"], "metadata": {"start_time": 100.88, "end_time": 127.54, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "b65ac3c3-dd43-49e7-b562-2cc9f35869d9": {"node_ids": ["aeaf360b-cc08-4e9a-a331-98e379b0b666"], "metadata": {"start_time": 127.76, "end_time": 154.22, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "e8718ede-0ca4-430f-a642-71818a7e12a0": {"node_ids": ["4a2d0a6a-8a60-45ca-8533-4c796493cc78"], "metadata": {"start_time": 154.48, "end_time": 175.86, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}}, "docstore/data": {"572d898e-7822-4c53-856c-92965b485e22": {"__data__": {"id_": "572d898e-7822-4c53-856c-92965b485e22", "embedding": null, "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "64904dc7-ffc6-4176-b64a-64f37d36015c", "node_type": "4", "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "a9fa4d0d1bce2f1dc8a516b5936fb9ab0d83d7cbc7a90ac6f508d494fa68f0fc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Hello friends, I am going to start a course on algorithms. Algorithm as a subject. Algorithm is a common subject for computer science engineering students. Most of the universities offer this course as a part of syllabus and this is a very core subject and very important subject. And students face some difficulties in some of the topics in this one. They could not understand them very clearly.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 396, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5d4cd244-609d-46a9-9565-179a94346806": {"__data__": {"id_": "5d4cd244-609d-46a9-9565-179a94346806", "embedding": null, "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ceff159b-7a59-45ce-930a-b7c2406056b4", "node_type": "4", "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "336390f869673fc93caf19d31ae008ef60097c19023e885e7c7405ccbf9b94ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So here I am going to make the subject more and more simple so that you can easily understand and even you can practice that and also remember it easily. The importance of the subject is that apart from theoretical examination, it is also important for competitive exams. And even programming contest, most of them are designed from this subject only. If any programming challenge is there, then mostly they pick up the questions from this subject.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 448, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "afe02446-e5a8-4c45-b26e-91f83027eca5": {"__data__": {"id_": "afe02446-e5a8-4c45-b26e-91f83027eca5", "embedding": null, "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ccb7c153-aac0-4f5d-9fab-c3008d172aef", "node_type": "4", "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "hash": "7e57206fe95b3731b94c0a71265ce769132ce4c9e113eb0c7e5bcfada26a47d6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So this is one of the challenging subject for the student because they get a chance to solve different type of problems and they get the strategy or approach for solving the problem. So some students fail to understand it properly so they could not get the approach or the strategy for solving the problems and they feel that they are lacking in the logic development or strategic development of a program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 406, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "eb9731af-f251-4d96-9141-c74ba61897b7": {"__data__": {"id_": "eb9731af-f251-4d96-9141-c74ba61897b7", "embedding": null, "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5d14a81b-912d-43db-89b5-65767a645e16", "node_type": "4", "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "hash": "0a053eb9db12d7c321c9e7e26d05d4171504e708cfe6ac7c93b06793295b2825", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So I will be covering in depth each and everything right from the basics to the depth. So that you will be able to answer any type of question from this one. I will cover the topics such that just you look at the question you can get the answer that I guarantee you. So the thing is I will cover those points which are useful for solving the questions.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 352, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0312a246-6ca3-4d96-a65e-102555896af9": {"__data__": {"id_": "0312a246-6ca3-4d96-a65e-102555896af9", "embedding": null, "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9c33a253-8e22-49f6-a5a2-7b1e3ee11693", "node_type": "4", "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "hash": "2fe908711069d923a9ac52b9b2e378427315b24f1c874b0aa2fe2967dee6ff93", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Some job interviews or even for any other exam or some entrance exams like gate exam, the cushions are framed from algorithms also. There is also one of the key subject there. So you will be able to answer any type of question from there. Now towards the lectures in the order, I'll be giving the numbers for each topic so that you follow that order, you follow the sequence because I may be breaking the major topic into small topics.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 435, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "524549d3-6f8c-4072-ba8d-42a33c1cc8a4": {"__data__": {"id_": "524549d3-6f8c-4072-ba8d-42a33c1cc8a4", "embedding": null, "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1bc64f52-1ecb-42a5-8a04-5b4f7c2d925f", "node_type": "4", "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "480fcfc2a237e6ba77e2fff1ebabf05d4fb5ce330272919b624cf83dc5e25074", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And one more important thing is your feedback is very important for me because already I have made few prepared few videos using presentations and other tools but now I am giving a lecture on whiteboard so maybe I am new for the video quality and audio quality all these things so technically once I get set up so I need your feedback so I can make my videos more and more better", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 379, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1c75a087-74d8-4447-bbd6-4dc252cbd147": {"__data__": {"id_": "1c75a087-74d8-4447-bbd6-4dc252cbd147", "embedding": null, "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0060c0ec-6d08-46a4-9e5f-d93ee3a38918", "node_type": "4", "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "hash": "6a2e25be111a47bb6bae3c5129e80849cda2092ffc07f5951c07f6a8da82eb8f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So your feedback is important for me to know how the video quality and audio quality you are getting it on your devices because I have checked on different devices and I found that the results are different. So what devices you are using, what I should improve, voice or what so I need your feedback. Let us start with just introduction. What is an Elcortone?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 359, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a400daa0-55ac-43fb-b1fc-f0e290e9be1d": {"__data__": {"id_": "a400daa0-55ac-43fb-b1fc-f0e290e9be1d", "embedding": null, "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "94ad3d3c-c4a9-43a9-b53e-84b5338959d6", "node_type": "4", "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "hash": "830aab74de03c0828a98481cbb72deb5cbc23d2df6bcb07c7f07f9379c7c1e07", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "What is an Elgortum? Why it is written? When it is written? Who will write it? Let us get the answers for these. See, algorithm as a definition, it's a common definition everybody knows that, it's a step by step procedure for solving a computational problem. Yes, it is a step by step procedure for solving a computational problem. Then what is a program? Program is also a step by step procedure for solving a problem. Then what is the difference between program and Elgortum?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 477, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9ed22d74-b331-4a99-a8d4-4f1e4c7c5f03": {"__data__": {"id_": "9ed22d74-b331-4a99-a8d4-4f1e4c7c5f03", "embedding": null, "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ccbb3e4d-94e4-408e-bfd4-f09e054b5be6", "node_type": "4", "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "hash": "c65c47d91c7d86a85ad8e8fc97834ebcb84551c0685952ccf84059faf7ec709f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So by comparing program and algorithm I can make you understand the importance of algorithm, meaning of algorithm. Let us take it. Here is an algorithm and here is a program. Let us see the first thing, first difference. If you know the software development life cycle, means the phases of developing a software project. In the phases of development of software project there are two important phases.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 401, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "53a51368-05a2-4a25-b91a-8ba92ac12750": {"__data__": {"id_": "53a51368-05a2-4a25-b91a-8ba92ac12750", "embedding": null, "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ee63a868-edaf-4917-b21d-39ece28a5cfe", "node_type": "4", "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "5fb38245dc6405b7af36ebda47abf4bafc417742a1334cb7c886cc1974ee35c4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "design phase and the implementation phase. Now if you are not aware of it let me tell you whatever you want to manufacture or construct something by engineering procedures then first step one of the important step is designing. First you do designing make your design perfect and thorough so that you can understand what you are going to construct what you are going to develop and when you are sure what you are going to develop then you start to develop.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 456, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ba7f6766-4281-490a-8201-580d46f6dc70": {"__data__": {"id_": "ba7f6766-4281-490a-8201-580d46f6dc70", "embedding": null, "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "514c767c-3cf3-4211-ad03-ec27083bcbe1", "node_type": "4", "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "bbf925bd7428bba5ebbeb0463f2aa04e17c3d80945545021e1a77bf817ce6a1d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can't develop anything, you cannot construct anything on trial and error basis. Like you constructed something, again no one is wrong, destroyed and again created a new one. So no. But it is easy in software engineering. Software engineer can write some program and change the mind, delete the program and again start writing them. So that's why we cannot get the feel that we have wasted so much amount of time in writing so useless program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7cc76331-addf-47f3-8923-e8ffdd108dd7": {"__data__": {"id_": "7cc76331-addf-47f3-8923-e8ffdd108dd7", "embedding": null, "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8300d9e3-c22e-42fa-9af9-40bc12db4561", "node_type": "4", "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "hash": "c3f3ae6ac56de307c373f644d10a2dfdfb863aa1541a07e3724d8d0baba22cdf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So the point is first you design and then you write the program. So at the design time what do you use? So you are not writing a program then what do you write? So that same program we write it in simple English like statements that are easy to understand without using proper syntax and we may not be writing on machine that is on computer, we may be writing on paper.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 369, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "962625cf-7f89-4c0a-98b4-927bd2220fa4": {"__data__": {"id_": "962625cf-7f89-4c0a-98b4-927bd2220fa4", "embedding": null, "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "db55e714-59f7-460b-a5ae-1dd8ba3eb49c", "node_type": "4", "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "hash": "b6726b0de7a7cb65c8a31aebeea36c34e820569bab75518dc7f9ee6b58a4264e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "or even if you are using a machine also then you will not be writing in some language, you will be writing in MS Word or notepad like application. So just you are getting familiar how your program is going to work. So that is nothing but an algorithm. So algorithms are written at design time and when the programs are written they are written at implementation time, design time and implementation.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 399, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8526f60d-16ce-470b-bac0-e399c902f1bc": {"__data__": {"id_": "8526f60d-16ce-470b-bac0-e399c902f1bc", "embedding": null, "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4739b662-c985-4a78-8365-3be3f81e809f", "node_type": "4", "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "112cf2c41b1e85acb4c3f9efa7214f83ad9c4cad253f587857bbbe21b74217c1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So first you make a design what your procedure is going to do, what your software is going to do, come up with some design and that design you convert into a program. Then what do you call the person who do designing? Whether the programmer does that? Yes if programmer is capable of doing that he can do it otherwise the person who will do this one should have the domain knowledge, the problems knowledge, knowledge about the problem and its solution.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 453, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c21fb300-09b4-4ae2-a642-b574a0d13237": {"__data__": {"id_": "c21fb300-09b4-4ae2-a642-b574a0d13237", "embedding": null, "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fb105139-5e40-49b1-8d7b-ee9e319ab502", "node_type": "4", "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "04bef88b5f0dcaff0f1f1e55bb08395099257accad5806c999de817df90386d1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He can give a solution, the one who understands the problem and has the domain knowledge. Suppose you are developing a software or an algorithm or a program for account then accountant can understand accounting better. Or if you are writing an application or developing an application for hospital then doctors or the administrative staff of a hospital can understand the system better than a programmer.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 404, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "64b184d6-fbf1-46f4-9a1c-0bf70a0970a6": {"__data__": {"id_": "64b184d6-fbf1-46f4-9a1c-0bf70a0970a6", "embedding": null, "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c43f7f3b-0f8a-4bca-9f51-0245d03f0914", "node_type": "4", "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "hash": "058e4c8e74d62b468c090a559f11923b8291d2249dee5646df7668d434b49533", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So mostly the one who is having a knowledge about the domain for which the application is being developed, they can write on the algorithm. So the person who writes should have domain knowledge. Who will write the programmer? Programmer? So programmers can also have domain knowledge. They can also have domain knowledge that he is acting as a designer and here he is acting as a programmer. Next, what is the language used for writing algorithm? You can use any language.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 472, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "dafb33fb-83a1-4fd3-9bf2-8e3db62a6c8f": {"__data__": {"id_": "dafb33fb-83a1-4fd3-9bf2-8e3db62a6c8f", "embedding": null, "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5d402238-b90b-4b99-8961-fd787a2ec2e5", "node_type": "4", "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "hash": "904eb1ca5e8cbc9017747b0ffa8acc872e73a567681c9265506ba9d1e7ecd56a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Any language, any language means English like language or you can use some mathematical notations. If you're writing English like language it will be like writing paras or samari but don't try it. If you want you can use mathematical notations also. It's better if you use mathematical notations. So any language can be used, any language means English language or some mathematical notations can be used as long as it is understandable by those people who are using it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 470, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ab54461c-ae79-4335-9292-0151ac0da536": {"__data__": {"id_": "ab54461c-ae79-4335-9292-0151ac0da536", "embedding": null, "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9bf8bbfc-97d1-4404-96af-2149fcb348f5", "node_type": "4", "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "hash": "6cb306b48dd708e5deb5b3abaafa45f1c4982f208e7cf7576ca213864b34f035", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Like if a designer has written an algorithm then the programmer will be writing a program for it. So designers should understand and also programmers should understand that one. So the team of programmers who are working on that project should be able to understand it. Then this is written only using programming language like C++, Java, Python. So you can use different languages for developing a program. The next one more important thing here", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a158490e-ca64-46f6-9639-4b3b3ddea73c": {"__data__": {"id_": "a158490e-ca64-46f6-9639-4b3b3ddea73c", "embedding": null, "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5307fa86-e0b3-449d-bf87-44ba12e187af", "node_type": "4", "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "hash": "75652f5626d3a034b092bfb06b8b469a8cc8e8192c64daa9dbfa68c5bc1fd114", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "When you write an L-Core, it's going to be hardware and software means operating system independent. It's not dependent on hardware, what machine you are going to use, what's the configuration of the machine and what's the operating system, either Linux operating system or Windows operating system, we don't bother about it. But when you write a program, it is dependent on hardware and operating system.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 405, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1bc671eb-4994-4efe-b3ad-ae57c9a59217": {"__data__": {"id_": "1bc671eb-4994-4efe-b3ad-ae57c9a59217", "embedding": null, "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cd545473-9fe9-4878-a453-7f72fd488cfc", "node_type": "4", "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "427c86b713e89ea116f9463b2a026e9bb592501861bb45c70c297ce3d7dc53dc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So when you develop the program you have to select some hardware on which you are going to run and also you have to select some operating system. You may be doing it for Linux or you may be doing it for Windows. So the method will be different and the environments are different. So this is important.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 301, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "68d50adc-f702-4ecd-8842-088faaea947b": {"__data__": {"id_": "68d50adc-f702-4ecd-8842-088faaea947b", "embedding": null, "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5ec61d05-fe86-4443-8c84-dfe1aa71d3b6", "node_type": "4", "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "hash": "398adcfe82df433050774ab48668f1483369eeb41352f55468c2daf162e8eefb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Then next, after writing an algorithm we analyze, analyze an algorithm. Means we will study the algorithm to find out are we achieving the results perfectly or not and our algorithm is efficient or not in terms of time and space. We will see what does it mean by analysis. So we will analyze an algorithm. What do we do with the program? So you don't have to study the program. All the program is there. Just run it and check it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 429, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4f65c026-0e6d-4d45-9d9e-b802047020b0": {"__data__": {"id_": "4f65c026-0e6d-4d45-9d9e-b802047020b0", "embedding": null, "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c3eb66fd-dafa-492c-817b-b8468baaaf77", "node_type": "4", "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "hash": "e22c92de10c79ced1f48786f7245fcf31b574c60484e8fe401ef8b54c4ab6634", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So we do testing, testing of the program. That's it. These are few differences between algorithm and program. These differences will help you understand what are algorithms. All right? Now one more thing I will tell you. The syntax of the language that we use, any language can be used but who actually write algorithms and who use them and who write the programs of programmers who are coming from university", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 409, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7fa10979-6731-4fac-93de-a54d1d1c8a1b": {"__data__": {"id_": "7fa10979-6731-4fac-93de-a54d1d1c8a1b", "embedding": null, "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b6cef169-aee3-4aa0-8668-6a0cf20e9278", "node_type": "4", "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "hash": "611977a7f35968dc31b638dc4ad4a8e586ab079f13855a5d4cbb0da874f60956", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "mostly university graduate knows at least C language. So nowadays mostly in the name of algorithm we write C language program only. So the benefit is that the advantage that everybody knows C language now. So instead of writing some other language to confuse we can use C language only so that everybody can understand. So even at a school level C language is being taught so everybody is familiar with C language. So I may be using C language syntax for writing algorithm.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 473, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7321a728-0590-463f-b6fb-dc68c0486209": {"__data__": {"id_": "7321a728-0590-463f-b6fb-dc68c0486209", "embedding": null, "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6c416079-407e-4fa4-900f-54a86c740742", "node_type": "4", "metadata": {"start_time": 0.04, "end_time": 27.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "a9fa4d0d1bce2f1dc8a516b5936fb9ab0d83d7cbc7a90ac6f508d494fa68f0fc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Hello friends, I am going to start a course on algorithms. Algorithm as a subject. Algorithm is a common subject for computer science engineering students. Most of the universities offer this course as a part of syllabus and this is a very core subject and very important subject. And students face some difficulties in some of the topics in this one. They could not understand them very clearly.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 396, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8233f550-84c5-4d0e-85ff-ceae9158a443": {"__data__": {"id_": "8233f550-84c5-4d0e-85ff-ceae9158a443", "embedding": null, "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1c91213c-db08-41b7-8ae0-f2339c97c99a", "node_type": "4", "metadata": {"start_time": 28.72, "end_time": 57.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "336390f869673fc93caf19d31ae008ef60097c19023e885e7c7405ccbf9b94ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So here I am going to make the subject more and more simple so that you can easily understand and even you can practice that and also remember it easily. The importance of the subject is that apart from theoretical examination, it is also important for competitive exams. And even programming contest, most of them are designed from this subject only. If any programming challenge is there, then mostly they pick up the questions from this subject.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 448, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8bc770ee-60c9-493e-b49c-6ce21b5ada5b": {"__data__": {"id_": "8bc770ee-60c9-493e-b49c-6ce21b5ada5b", "embedding": null, "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9daa5958-aac1-48c6-bfb2-b8942123b14d", "node_type": "4", "metadata": {"start_time": 58.38, "end_time": 84.54, "file_path": "transcript.json", "video_id": "god"}, "hash": "7e57206fe95b3731b94c0a71265ce769132ce4c9e113eb0c7e5bcfada26a47d6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So this is one of the challenging subject for the student because they get a chance to solve different type of problems and they get the strategy or approach for solving the problem. So some students fail to understand it properly so they could not get the approach or the strategy for solving the problems and they feel that they are lacking in the logic development or strategic development of a program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 406, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f7731c17-e472-4c16-9529-da3620b4d0e6": {"__data__": {"id_": "f7731c17-e472-4c16-9529-da3620b4d0e6", "embedding": null, "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8600a13c-939a-4a6b-a105-90add69b5dbb", "node_type": "4", "metadata": {"start_time": 85.48, "end_time": 114.04, "file_path": "transcript.json", "video_id": "god"}, "hash": "0a053eb9db12d7c321c9e7e26d05d4171504e708cfe6ac7c93b06793295b2825", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So I will be covering in depth each and everything right from the basics to the depth. So that you will be able to answer any type of question from this one. I will cover the topics such that just you look at the question you can get the answer that I guarantee you. So the thing is I will cover those points which are useful for solving the questions.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 352, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "dcc6e26f-426c-4f34-92f6-88795d92bff7": {"__data__": {"id_": "dcc6e26f-426c-4f34-92f6-88795d92bff7", "embedding": null, "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aee6e897-92e3-44fa-93c3-44eed568f9b7", "node_type": "4", "metadata": {"start_time": 115.92, "end_time": 142.32, "file_path": "transcript.json", "video_id": "god"}, "hash": "2fe908711069d923a9ac52b9b2e378427315b24f1c874b0aa2fe2967dee6ff93", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Some job interviews or even for any other exam or some entrance exams like gate exam, the cushions are framed from algorithms also. There is also one of the key subject there. So you will be able to answer any type of question from there. Now towards the lectures in the order, I'll be giving the numbers for each topic so that you follow that order, you follow the sequence because I may be breaking the major topic into small topics.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 435, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "19f4b50f-e198-4d1e-9bd0-f2ae3e4fadac": {"__data__": {"id_": "19f4b50f-e198-4d1e-9bd0-f2ae3e4fadac", "embedding": null, "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "75ca3661-3df4-4ca4-a540-d26f5b4d7098", "node_type": "4", "metadata": {"start_time": 143.44, "end_time": 167.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "480fcfc2a237e6ba77e2fff1ebabf05d4fb5ce330272919b624cf83dc5e25074", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And one more important thing is your feedback is very important for me because already I have made few prepared few videos using presentations and other tools but now I am giving a lecture on whiteboard so maybe I am new for the video quality and audio quality all these things so technically once I get set up so I need your feedback so I can make my videos more and more better", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 379, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "394b9db6-94d5-4626-8440-ae08915e1d73": {"__data__": {"id_": "394b9db6-94d5-4626-8440-ae08915e1d73", "embedding": null, "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "006505af-f1c0-4d87-81e9-5dcdd89173ce", "node_type": "4", "metadata": {"start_time": 168.3, "end_time": 191.8, "file_path": "transcript.json", "video_id": "god"}, "hash": "6a2e25be111a47bb6bae3c5129e80849cda2092ffc07f5951c07f6a8da82eb8f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So your feedback is important for me to know how the video quality and audio quality you are getting it on your devices because I have checked on different devices and I found that the results are different. So what devices you are using, what I should improve, voice or what so I need your feedback. Let us start with just introduction. What is an Elcortone?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 359, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b6a12791-a907-46b2-9b3c-0d5440e9116c": {"__data__": {"id_": "b6a12791-a907-46b2-9b3c-0d5440e9116c", "embedding": null, "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "db95727f-89e6-4d3a-8205-3ecf4e152900", "node_type": "4", "metadata": {"start_time": 192.58, "end_time": 223.1, "file_path": "transcript.json", "video_id": "god"}, "hash": "830aab74de03c0828a98481cbb72deb5cbc23d2df6bcb07c7f07f9379c7c1e07", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "What is an Elgortum? Why it is written? When it is written? Who will write it? Let us get the answers for these. See, algorithm as a definition, it's a common definition everybody knows that, it's a step by step procedure for solving a computational problem. Yes, it is a step by step procedure for solving a computational problem. Then what is a program? Program is also a step by step procedure for solving a problem. Then what is the difference between program and Elgortum?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 477, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b0c32b8a-00a2-4777-a981-3da67e2d0731": {"__data__": {"id_": "b0c32b8a-00a2-4777-a981-3da67e2d0731", "embedding": null, "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5f739082-1605-416c-85c8-e4394574fb33", "node_type": "4", "metadata": {"start_time": 223.54, "end_time": 266.34, "file_path": "transcript.json", "video_id": "god"}, "hash": "c65c47d91c7d86a85ad8e8fc97834ebcb84551c0685952ccf84059faf7ec709f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So by comparing program and algorithm I can make you understand the importance of algorithm, meaning of algorithm. Let us take it. Here is an algorithm and here is a program. Let us see the first thing, first difference. If you know the software development life cycle, means the phases of developing a software project. In the phases of development of software project there are two important phases.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 401, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3dc44c34-c210-4112-bc33-db76aa3d49e5": {"__data__": {"id_": "3dc44c34-c210-4112-bc33-db76aa3d49e5", "embedding": null, "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a5ae1367-ed5c-420a-b7bb-3ebcc80cf13d", "node_type": "4", "metadata": {"start_time": 266.82, "end_time": 294.96, "file_path": "transcript.json", "video_id": "god"}, "hash": "5fb38245dc6405b7af36ebda47abf4bafc417742a1334cb7c886cc1974ee35c4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "design phase and the implementation phase. Now if you are not aware of it let me tell you whatever you want to manufacture or construct something by engineering procedures then first step one of the important step is designing. First you do designing make your design perfect and thorough so that you can understand what you are going to construct what you are going to develop and when you are sure what you are going to develop then you start to develop.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 456, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1654aca6-6d13-43cb-aac0-a12a2563414b": {"__data__": {"id_": "1654aca6-6d13-43cb-aac0-a12a2563414b", "embedding": null, "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "74e4baed-24b0-4770-898e-2cb2471e1dc1", "node_type": "4", "metadata": {"start_time": 296.28, "end_time": 325.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "bbf925bd7428bba5ebbeb0463f2aa04e17c3d80945545021e1a77bf817ce6a1d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can't develop anything, you cannot construct anything on trial and error basis. Like you constructed something, again no one is wrong, destroyed and again created a new one. So no. But it is easy in software engineering. Software engineer can write some program and change the mind, delete the program and again start writing them. So that's why we cannot get the feel that we have wasted so much amount of time in writing so useless program.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "32def915-32ad-42e4-a653-645cf75b419e": {"__data__": {"id_": "32def915-32ad-42e4-a653-645cf75b419e", "embedding": null, "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8e7819d6-14c5-4259-ab94-2ae78e4f6534", "node_type": "4", "metadata": {"start_time": 326.28, "end_time": 349.98, "file_path": "transcript.json", "video_id": "god"}, "hash": "c3f3ae6ac56de307c373f644d10a2dfdfb863aa1541a07e3724d8d0baba22cdf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So the point is first you design and then you write the program. So at the design time what do you use? So you are not writing a program then what do you write? So that same program we write it in simple English like statements that are easy to understand without using proper syntax and we may not be writing on machine that is on computer, we may be writing on paper.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 369, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "083531dd-04f0-4e93-9b35-cdbae9f5e4d8": {"__data__": {"id_": "083531dd-04f0-4e93-9b35-cdbae9f5e4d8", "embedding": null, "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "92108841-daef-45b2-81f9-b90b043534f8", "node_type": "4", "metadata": {"start_time": 351.44, "end_time": 388.94, "file_path": "transcript.json", "video_id": "god"}, "hash": "b6726b0de7a7cb65c8a31aebeea36c34e820569bab75518dc7f9ee6b58a4264e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "or even if you are using a machine also then you will not be writing in some language, you will be writing in MS Word or notepad like application. So just you are getting familiar how your program is going to work. So that is nothing but an algorithm. So algorithms are written at design time and when the programs are written they are written at implementation time, design time and implementation.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 399, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6df07390-7053-4693-bf3c-12a276cf58f3": {"__data__": {"id_": "6df07390-7053-4693-bf3c-12a276cf58f3", "embedding": null, "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "89cad037-3cd0-40c1-a13d-48954b152a50", "node_type": "4", "metadata": {"start_time": 390.86, "end_time": 419.62, "file_path": "transcript.json", "video_id": "god"}, "hash": "112cf2c41b1e85acb4c3f9efa7214f83ad9c4cad253f587857bbbe21b74217c1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So first you make a design what your procedure is going to do, what your software is going to do, come up with some design and that design you convert into a program. Then what do you call the person who do designing? Whether the programmer does that? Yes if programmer is capable of doing that he can do it otherwise the person who will do this one should have the domain knowledge, the problems knowledge, knowledge about the problem and its solution.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 453, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2b49e0ea-8faf-4967-84c5-545c35825f3d": {"__data__": {"id_": "2b49e0ea-8faf-4967-84c5-545c35825f3d", "embedding": null, "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "84eb41fb-4b99-42fb-8263-6b6773e745e7", "node_type": "4", "metadata": {"start_time": 420.02, "end_time": 445.64, "file_path": "transcript.json", "video_id": "god"}, "hash": "04bef88b5f0dcaff0f1f1e55bb08395099257accad5806c999de817df90386d1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He can give a solution, the one who understands the problem and has the domain knowledge. Suppose you are developing a software or an algorithm or a program for account then accountant can understand accounting better. Or if you are writing an application or developing an application for hospital then doctors or the administrative staff of a hospital can understand the system better than a programmer.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 404, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1c9e2271-0f56-4ba5-8691-afaf0954aab9": {"__data__": {"id_": "1c9e2271-0f56-4ba5-8691-afaf0954aab9", "embedding": null, "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d944846f-285b-4641-8524-1486d47b3bb7", "node_type": "4", "metadata": {"start_time": 445.9, "end_time": 490.18, "file_path": "transcript.json", "video_id": "god"}, "hash": "058e4c8e74d62b468c090a559f11923b8291d2249dee5646df7668d434b49533", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So mostly the one who is having a knowledge about the domain for which the application is being developed, they can write on the algorithm. So the person who writes should have domain knowledge. Who will write the programmer? Programmer? So programmers can also have domain knowledge. They can also have domain knowledge that he is acting as a designer and here he is acting as a programmer. Next, what is the language used for writing algorithm? You can use any language.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 472, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5f25826d-2dfd-4b55-837e-0d8bbd8028d4": {"__data__": {"id_": "5f25826d-2dfd-4b55-837e-0d8bbd8028d4", "embedding": null, "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "34a2394e-a459-45d5-ba17-748ca422dd7e", "node_type": "4", "metadata": {"start_time": 493.08, "end_time": 521.5, "file_path": "transcript.json", "video_id": "god"}, "hash": "904eb1ca5e8cbc9017747b0ffa8acc872e73a567681c9265506ba9d1e7ecd56a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Any language, any language means English like language or you can use some mathematical notations. If you're writing English like language it will be like writing paras or samari but don't try it. If you want you can use mathematical notations also. It's better if you use mathematical notations. So any language can be used, any language means English language or some mathematical notations can be used as long as it is understandable by those people who are using it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 470, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9358c619-5676-4724-99af-e72a5bd80b53": {"__data__": {"id_": "9358c619-5676-4724-99af-e72a5bd80b53", "embedding": null, "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b1e61df7-4462-4b84-a4ed-8c78ef71a548", "node_type": "4", "metadata": {"start_time": 522.72, "end_time": 558.56, "file_path": "transcript.json", "video_id": "god"}, "hash": "6cb306b48dd708e5deb5b3abaafa45f1c4982f208e7cf7576ca213864b34f035", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Like if a designer has written an algorithm then the programmer will be writing a program for it. So designers should understand and also programmers should understand that one. So the team of programmers who are working on that project should be able to understand it. Then this is written only using programming language like C++, Java, Python. So you can use different languages for developing a program. The next one more important thing here", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 446, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c6e2a2ea-3bf6-48f9-879c-d60bc1e4c0b7": {"__data__": {"id_": "c6e2a2ea-3bf6-48f9-879c-d60bc1e4c0b7", "embedding": null, "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "734ac39c-3d14-4486-a55b-0f3b3ca8197b", "node_type": "4", "metadata": {"start_time": 559.52, "end_time": 584.82, "file_path": "transcript.json", "video_id": "god"}, "hash": "75652f5626d3a034b092bfb06b8b469a8cc8e8192c64daa9dbfa68c5bc1fd114", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "When you write an L-Core, it's going to be hardware and software means operating system independent. It's not dependent on hardware, what machine you are going to use, what's the configuration of the machine and what's the operating system, either Linux operating system or Windows operating system, we don't bother about it. But when you write a program, it is dependent on hardware and operating system.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 405, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "57539145-be03-4bd8-b252-63dbc5d1c57e": {"__data__": {"id_": "57539145-be03-4bd8-b252-63dbc5d1c57e", "embedding": null, "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4e7531b9-84ba-4055-8863-8f9a285a4613", "node_type": "4", "metadata": {"start_time": 585.32, "end_time": 602.9, "file_path": "transcript.json", "video_id": "god"}, "hash": "427c86b713e89ea116f9463b2a026e9bb592501861bb45c70c297ce3d7dc53dc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So when you develop the program you have to select some hardware on which you are going to run and also you have to select some operating system. You may be doing it for Linux or you may be doing it for Windows. So the method will be different and the environments are different. So this is important.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 301, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "805bc548-6810-46e2-ac54-8de2729c725c": {"__data__": {"id_": "805bc548-6810-46e2-ac54-8de2729c725c", "embedding": null, "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6d053aa8-8e93-4ec6-a42d-ec95a03408cc", "node_type": "4", "metadata": {"start_time": 604.6, "end_time": 638.76, "file_path": "transcript.json", "video_id": "god"}, "hash": "398adcfe82df433050774ab48668f1483369eeb41352f55468c2daf162e8eefb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Then next, after writing an algorithm we analyze, analyze an algorithm. Means we will study the algorithm to find out are we achieving the results perfectly or not and our algorithm is efficient or not in terms of time and space. We will see what does it mean by analysis. So we will analyze an algorithm. What do we do with the program? So you don't have to study the program. All the program is there. Just run it and check it.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 429, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b98d124d-0b57-4ba1-b7ab-e48b1636d89d": {"__data__": {"id_": "b98d124d-0b57-4ba1-b7ab-e48b1636d89d", "embedding": null, "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c6cc0bff-ef7d-4a73-86c2-befb834c0fe5", "node_type": "4", "metadata": {"start_time": 639.14, "end_time": 674.72, "file_path": "transcript.json", "video_id": "god"}, "hash": "e22c92de10c79ced1f48786f7245fcf31b574c60484e8fe401ef8b54c4ab6634", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So we do testing, testing of the program. That's it. These are few differences between algorithm and program. These differences will help you understand what are algorithms. All right? Now one more thing I will tell you. The syntax of the language that we use, any language can be used but who actually write algorithms and who use them and who write the programs of programmers who are coming from university", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 409, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "76a939d3-c1c4-4f5e-872b-313303cf8093": {"__data__": {"id_": "76a939d3-c1c4-4f5e-872b-313303cf8093", "embedding": null, "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f13c080a-aadc-46fe-94b9-c91022f2fecb", "node_type": "4", "metadata": {"start_time": 675.24, "end_time": 707.38, "file_path": "transcript.json", "video_id": "god"}, "hash": "611977a7f35968dc31b638dc4ad4a8e586ab079f13855a5d4cbb0da874f60956", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "mostly university graduate knows at least C language. So nowadays mostly in the name of algorithm we write C language program only. So the benefit is that the advantage that everybody knows C language now. So instead of writing some other language to confuse we can use C language only so that everybody can understand. So even at a school level C language is being taught so everybody is familiar with C language. So I may be using C language syntax for writing algorithm.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 473, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1870ccef-95f4-4833-94cf-bf6d22d049ff": {"__data__": {"id_": "1870ccef-95f4-4833-94cf-bf6d22d049ff", "embedding": null, "metadata": {"start_time": 0.0, "end_time": 23.2, "file_path": "transcript.json", "video_id": "maling"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aa9d7295-e5e3-44e9-b291-50dd6dc446c6", "node_type": "4", "metadata": {"start_time": 0.0, "end_time": 23.2, "file_path": "transcript.json", "video_id": "maling"}, "hash": "e678239e346c58644a9b6eebff24892bd0c99e8e05db6036e3e4caeb58e945e4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "That's it <PERSON><PERSON>. <PERSON> has been cleaned up by <PERSON><PERSON> so we'll pop back up for this boys. Yeah this was very much on the cards and <PERSON><PERSON> was looking for this angle. He's cleaned him up with a brilliant delivery. Created a gap between batten ball, batten pad.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 269, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a627dd3e-b472-44d0-9d65-e5bd1faa6342": {"__data__": {"id_": "a627dd3e-b472-44d0-9d65-e5bd1faa6342", "embedding": null, "metadata": {"start_time": 23.64, "end_time": 62.4, "file_path": "transcript.json", "video_id": "maling"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5f981593-c5ea-4493-afea-26ecf87916bd", "node_type": "4", "metadata": {"start_time": 23.64, "end_time": 62.4, "file_path": "transcript.json", "video_id": "maling"}, "hash": "7392f17fd951c080dddc34fdda9509d4d7fc6a4493571987a1e9fa2bc125a177", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "is mostly a massive striker down towards Midwicket and Square Lake. This time got it all wrong and <PERSON><PERSON> with a finger raise has done it again from Sri Lanka with an early breakthrough. Gone for 12-15-4. Breaking on the pad, asking the question will they go upstairs? They have. Oh close. And <PERSON><PERSON><PERSON>'s hitting. Fabulous review taken by <PERSON><PERSON>. Brilliant piece of cricket. Yes, that's a confirmation.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 409, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "25bf9469-5c7c-4a38-ba81-fbcd400656aa": {"__data__": {"id_": "25bf9469-5c7c-4a38-ba81-fbcd400656aa", "embedding": null, "metadata": {"start_time": 62.8, "end_time": 98.08, "file_path": "transcript.json", "video_id": "maling"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "520bde88-e758-4862-8835-f3ddeab82473", "node_type": "4", "metadata": {"start_time": 62.8, "end_time": 98.08, "file_path": "transcript.json", "video_id": "maling"}, "hash": "587e251462edcbcd0f5a914acba6d4df05ebe9af6fe2c8bb001d4ebf0b43b026", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Brilliant review, Sri Lanka got two wickets in two balls. Rather than goals of the very first delivery. The title starts Sri Lanka needed. He's on a hat-trick. <PERSON><PERSON><PERSON>, <PERSON> is a new batsman. Well, the crowd's gone wild. The mobile phones are on. The lights are on. <PERSON> on strike. Will this be the hat-trick? Let's see. <PERSON><PERSON><PERSON>! You beauty! You got a hat-trick!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 390, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4f506000-4b94-4eaa-8233-1c74a1544cbd": {"__data__": {"id_": "4f506000-4b94-4eaa-8233-1c74a1544cbd", "embedding": null, "metadata": {"start_time": 98.58, "end_time": 128.5, "file_path": "transcript.json", "video_id": "maling"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "16b12a71-b70e-4b93-aa74-79d5f7409815", "node_type": "4", "metadata": {"start_time": 98.58, "end_time": 128.5, "file_path": "transcript.json", "video_id": "maling"}, "hash": "f9e3646d598029416e1b23b288d0536407fd79ae3e58d2bf578dfcdd6168579c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And it's 15 for 3-1, brilliant bowling by the Sri Lankan captain. Well, he's turned it around and what a magnificent delivery. He's looking at the big screen, he's looking where they'll get a reprieve or not. So we talked earlier about him creating a bit of drama. This could be a massive anticlimax.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 300, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b7265ee0-0789-48ea-8ef3-9820939dd4fa": {"__data__": {"id_": "b7265ee0-0789-48ea-8ef3-9820939dd4fa", "embedding": null, "metadata": {"start_time": 130.06, "end_time": 159.7, "file_path": "transcript.json", "video_id": "maling"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e97b9166-b365-43c1-8bfd-17860579f884", "node_type": "4", "metadata": {"start_time": 130.06, "end_time": 159.7, "file_path": "transcript.json", "video_id": "maling"}, "hash": "67f0b8a52a981f36123ac41fb85f3872502c06b001921fd11e56056168b0dc35", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "is some part of that heel behind the crease. That is what the third umpire is looking to be there. What do you think, folks? At home, what do you think? It's mighty tight. Will it be a no-ball? Will it be a no-ball? Let's see what happens. I think it's a confirmation. The third umpire confirms he's having something behind the line. Oh, what a moment. What a moment. And lastly, smiling a good smile.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 401, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7409083e-d8c6-4247-9653-a97e88bed66b": {"__data__": {"id_": "7409083e-d8c6-4247-9653-a97e88bed66b", "embedding": null, "metadata": {"start_time": 160.42, "end_time": 200.34, "file_path": "transcript.json", "video_id": "maling"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ee4afbae-18fd-4a33-b5d8-4cb8af2dd521", "node_type": "4", "metadata": {"start_time": 160.42, "end_time": 200.34, "file_path": "transcript.json", "video_id": "maling"}, "hash": "5fb8afb4cbf60ad12da931af145eb139571069a5ebcac781386c408e97dca268", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He got home gone without scoring. It's drama here. New Zealand 15 for 3. <PERSON><PERSON><PERSON>. I mean, first ball. He had no clue whatsoever. The angle of the bat suggested that he had missed the ball when it was in its ball-flut. And that hat trick has made the day for the Gras and it was mighty close whether it was a no-ball or not. Some part of the heel was red as being behind the popping breeze. Yeah, that's him. Number 99, no more. A strike on the pass!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 451, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c4eb940c-41e6-470c-a021-bf810a548ed4": {"__data__": {"id_": "c4eb940c-41e6-470c-a021-bf810a548ed4", "embedding": null, "metadata": {"start_time": 201.1, "end_time": 235.009, "file_path": "transcript.json", "video_id": "maling"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "34530840-ff1e-4b97-a788-c4eb8d01c037", "node_type": "4", "metadata": {"start_time": 201.1, "end_time": 235.009, "file_path": "transcript.json", "video_id": "maling"}, "hash": "ae947121d5426b4e887fa1152d64deae2132a07bd0e7199f8c2cd347d67ac17a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "4-4! True good to be true! It's true good to be true! 4-4 for <PERSON><PERSON>. New Zealand now 15-4 for 4. <PERSON> gets the seed back fuller leg ball and plunge in front of the stops. Dead, dark, fantastic, fabulous piece of pulling, accuracy, sideways movement and 4-4. <PERSON><PERSON> is unstoppable, 15-4 for 4.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 318, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4a0415f1-0d7f-44ec-886b-ed99a02df249": {"__data__": {"id_": "4a0415f1-0d7f-44ec-886b-ed99a02df249", "embedding": null, "metadata": {"start_time": 0.0, "end_time": 18.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2aba7153-a41d-4184-aa63-0008e4133955", "node_type": "4", "metadata": {"start_time": 0.0, "end_time": 18.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "abea6f58cb107b083b5ea2baaad8a7ab578746a298512c9a8e902d50b7748f15", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<PERSON><PERSON><PERSON>, so I don't know if anyone's seen the film, I think it's a masterpiece, <PERSON><PERSON><PERSON>. And I was interested in <PERSON><PERSON><PERSON>. I got interested in him as a character quite a long time ago because I discovered that he gave the BBC wreath lectures in 1953 and they've almost been obliterated from history because they're really hard.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 338, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "18bc7bdb-a32f-4ace-86b9-ed0845d1ebbb": {"__data__": {"id_": "18bc7bdb-a32f-4ace-86b9-ed0845d1ebbb", "embedding": null, "metadata": {"start_time": 19.68, "end_time": 40.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e93c3af0-1d7b-4ff5-adf6-1a494956c1e8", "node_type": "4", "metadata": {"start_time": 19.68, "end_time": 40.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "6b42efbc5c8b10eb959400b2a51a0da82393d17370697dd6feac299303dd6a98", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "One of the things I grew up watching was one of the people was <PERSON> who I don't know if anyone's old enough will remember I think <PERSON>'s cosmos and he was on the BBC 13 episodes 13 weeks and there's very little science and television at that point and three channels or something. But cosmos was on and <PERSON><PERSON>.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 321, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3ddbf740-943b-4e5b-bf9b-2a0c29cf66d3": {"__data__": {"id_": "3ddbf740-943b-4e5b-bf9b-2a0c29cf66d3", "embedding": null, "metadata": {"start_time": 40.98, "end_time": 58.96, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "bb5a72b1-e5d6-4ca4-8a66-ab30791f1d84", "node_type": "4", "metadata": {"start_time": 40.98, "end_time": 58.96, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "ef346cb5ae3cf7d8aa1b4326acd0439e194fa2896aaf0a5ff1be5152f3386f47", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "presented astronomy and you talk about astronomy in the solar system and the universe but he put it in a context the context of our civilization. And he was explicit that this way of thinking this way of interrogating nature trying to understand the natural world.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 264, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1bb44b00-df83-4e56-946e-b8f37c6dcdea": {"__data__": {"id_": "1bb44b00-df83-4e56-946e-b8f37c6dcdea", "embedding": null, "metadata": {"start_time": 59.5, "end_time": 72.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cd7bc4a5-ed58-4cd8-a8b9-ef4cf02ec864", "node_type": "4", "metadata": {"start_time": 59.5, "end_time": 72.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "d9de66701629057284cc1334d18234fa65996743a5f896485ca0a0db1f4c0f66", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Is vital for our survival as a species is it's out central to the is one of the one of the necessary foundations of civilization so is a polemic and.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 149, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4f79e5ae-16f7-464b-91c2-e79e767091b5": {"__data__": {"id_": "4f79e5ae-16f7-464b-91c2-e79e767091b5", "embedding": null, "metadata": {"start_time": 72.72, "end_time": 91.12, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "16f079a9-d376-47ae-ac2e-6c96ea3c5e7b", "node_type": "4", "metadata": {"start_time": 72.72, "end_time": 91.12, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "c7d143b1e17ce454edb8db644d0457812dadd290995b5788b91ca294b6471ab2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So I genuinely think that science has the thought process, the things we discover and that way of thinking about the world, acquiring reliable knowledge about the world is the way we do it. That's important. So I do have a", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 222, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "72d783d4-8ffd-46bf-a5c1-b301a304cdad": {"__data__": {"id_": "72d783d4-8ffd-46bf-a5c1-b301a304cdad", "embedding": null, "metadata": {"start_time": 91.34, "end_time": 115.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5097bf99-a766-4963-995d-bbcdbf3d2604", "node_type": "4", "metadata": {"start_time": 91.34, "end_time": 115.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "ce1a4652d723fd6d1a46007fc1a0d517fd444c966d50a66863f685a4d5b0ac99", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "an agenda when I talk about science on television or live shows or whatever it is because I think that it's important. So there is an underlying feeling there that I have that these, I said once, someone asked me once, why do you want to present a program on BBC One, for example, about astronomy?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 297, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0c5173fa-b1a4-4791-bad7-135a2d832bc1": {"__data__": {"id_": "0c5173fa-b1a4-4791-bad7-135a2d832bc1", "embedding": null, "metadata": {"start_time": 115.54, "end_time": 144.2, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "871c737d-2f53-4795-b927-8771cc087290", "node_type": "4", "metadata": {"start_time": 115.54, "end_time": 144.2, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "cd7c4e93dd1f53fd3556d536c797142e9f5fe9dbdc51008f789dc94bbd141119", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And I said that I think science is too important not to be part of popular culture. So I really believe that. So if you believe it and you get a chance, look again, you get the chance, the platform to do it, then it would be ridiculous not to try and take that. I do think that scientists, if they want to and get the chance, have in some sense a responsibility to talk about it. Because you know, I mean, and there are obvious things we could talk about.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 455, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "56c10654-a30e-46c5-83d1-62aa29fb096e": {"__data__": {"id_": "56c10654-a30e-46c5-83d1-62aa29fb096e", "embedding": null, "metadata": {"start_time": 144.34, "end_time": 170.08, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8b9da95a-e24a-462e-a8a9-b1deabdc680f", "node_type": "4", "metadata": {"start_time": 144.34, "end_time": 170.08, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "554da75241ee23d26d8ef29c3e4900645c4060e295208c011409ce08199729cf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "One topic, actually, is <PERSON><PERSON><PERSON>. So I don't know if anyone's seen the film, I think it's a masterpiece, <PERSON><PERSON><PERSON>. And I was interested in <PERSON><PERSON><PERSON>. I got interested in him as a character quite a long time ago because I discovered that he gave the BBC wreath lectures in 1953. And they've almost been obliterated from history because they're really hard. And so we tend to think of it. But in those lectures, when I found them,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 436, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "fb6d0982-cd48-40be-ab8b-6d1400a184b5": {"__data__": {"id_": "fb6d0982-cd48-40be-ab8b-6d1400a184b5", "embedding": null, "metadata": {"start_time": 170.3, "end_time": 183.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f9170471-51e9-45bd-a99c-b4d9a80dc533", "node_type": "4", "metadata": {"start_time": 170.3, "end_time": 183.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "b4df039fdf4b65bde2c0e6d293c591232a029792b3dd515508b349597d77afef", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "I saw this scientist who obviously famously played a role in developing the atomic bomb, so in delivering the means by which we might destroy ourselves as a civilization, and he obviously knew that and it tortured him.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 218, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f0f10494-e756-4fe5-9440-8ee3938e5142": {"__data__": {"id_": "f0f10494-e756-4fe5-9440-8ee3938e5142", "embedding": null, "metadata": {"start_time": 183.88, "end_time": 211.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "081b62b7-c77c-4de1-a0c5-d4b4a607f2c9", "node_type": "4", "metadata": {"start_time": 183.88, "end_time": 211.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "c8491fcedd9ce0de205289516f55fefb6b29d33ee5573e0b5b61af39a3856262", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So that what I made him do was think about how we might avoid doing that. So he started thinking about politics and society and civilization. And are there any lessons from this tremendously successful approach to requiring reliable knowledge, which we call science? Are there any lessons that we could apply in wider society? Certainly wasn't saying that scientists should run the world, right?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 395, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7f6fa760-098f-4d38-bb21-f74e23414e4a": {"__data__": {"id_": "7f6fa760-098f-4d38-bb21-f74e23414e4a", "embedding": null, "metadata": {"start_time": 211.02, "end_time": 239.8, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fd2d6cd7-dcbc-49d8-8d3a-aff857b3e30f", "node_type": "4", "metadata": {"start_time": 211.02, "end_time": 239.8, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "bb119671b79e61d09ca0ca91db1e1571f5e267df0e8c017ba33d85db929eb25e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "He clearly decided that was a bad idea. But, and some of those lessons, you might call them transferable skills, which perhaps goes back to the heart of what we're talking about, are important, I think. And one of them is not kidding yourself, not deluding yourself into thinking that you understand something, not not. So it's a really understanding that the world is very complicated. And there are many ways of,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 414, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ee813a97-ad59-4902-af1b-5e230dc98696": {"__data__": {"id_": "ee813a97-ad59-4902-af1b-5e230dc98696", "embedding": null, "metadata": {"start_time": 239.46, "end_time": 264.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0b070a7a-a4c0-4554-a7fd-b7102d705b65", "node_type": "4", "metadata": {"start_time": 239.46, "end_time": 264.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "0ba869863c7fa0ab2c4700b6c21515060d76138020045c940ded89d894b2cb69", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "It's difficult to understand a black hole, right? A collapsing star. But it's also difficult to understand how to run a society. It's really tricky. So there aren't simple answers. And from we could talk, I'll stop talking about that, but I could talk about it forever. But that I think is always when I got the opportunity to talk about science in my mind,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 357, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0caa6dbd-d180-4419-8184-ef74fc5993ac": {"__data__": {"id_": "0caa6dbd-d180-4419-8184-ef74fc5993ac", "embedding": null, "metadata": {"start_time": 264.78, "end_time": 291.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9fd09855-435a-44d5-a61d-61d5662819a6", "node_type": "4", "metadata": {"start_time": 264.78, "end_time": 291.66, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "90f7981e9f182ca1e53987d4970f08f5422528a486b76d93791c0d202bd33739", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "that I enjoy talking about it, I think it's wonderful, I get excited about talking about these big ideas. But also in my mind there was also the things that <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and other of my heroes had also said, which is that there should be, there is a responsibility to talk about this way of thinking and the things we've discovered. Which leads us into one of the areas that I think you're masterful at if you don't mind me saying,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 457, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "39e39144-9320-47df-affc-ca4e5bfa8202": {"__data__": {"id_": "39e39144-9320-47df-affc-ca4e5bfa8202", "embedding": null, "metadata": {"start_time": 291.86, "end_time": 314.34, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0f734f30-9135-4eae-b5c8-3d9b74c3b2ff", "node_type": "4", "metadata": {"start_time": 291.86, "end_time": 314.34, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "ad220ea7e13003651e67e50a40eb060390290008aafe6c0ce7e367755e2d8f16", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "is overcoming a common trait that you see with lots of intelligent people, the curse of knowledge, that you know an awful lot but your ability to translate that knowledge and make it accessible to seven-year-old children or to a mass audience on the BBC is a unique skill set in itself and I'm interested", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 304, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "07eb30d8-a116-4278-aa01-b765a7a92715": {"__data__": {"id_": "07eb30d8-a116-4278-aa01-b765a7a92715", "embedding": null, "metadata": {"start_time": 314.6, "end_time": 332.06, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8ca51b17-ece2-4bf4-ac37-c36ee91ea04c", "node_type": "4", "metadata": {"start_time": 314.6, "end_time": 332.06, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "b0e03a034ecd78eec7b4cb691e9711f2acf4a788a9c5f1698306044accf4918f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "and exploring how do you go about being able to communicate these complex difficult ideas in a way that people can understand and start to get to grips with. You know in part it's about that we talked earlier about honesty, being honest with yourself.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 251, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8396927f-4a06-4f7f-a5df-b6fd8e022bac": {"__data__": {"id_": "8396927f-4a06-4f7f-a5df-b6fd8e022bac", "embedding": null, "metadata": {"start_time": 332.5, "end_time": 360.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f059ae34-2f2d-4157-ad3f-8f33373183e1", "node_type": "4", "metadata": {"start_time": 332.5, "end_time": 360.28, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "22b0a3804e8dd55b19204e89c57128f2ceb46d953837067a67b288ed84fca199", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "about how difficult it is to understand some of these concepts. So if you've been through the process and I find this, I'm quite slow quite often. I just, I don't understand that, I understand that, I understand. Oh yeah, that's it. Then what I do usually is just talk about the way that I understand something. And it's quite often the way that a seven year old would understand something because you've been, I find anyway, if you're honest with yourself then", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 461, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d24e1bab-cb66-482c-902a-06327977c2f2": {"__data__": {"id_": "d24e1bab-cb66-482c-902a-06327977c2f2", "embedding": null, "metadata": {"start_time": 360.14, "end_time": 366.98, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "586c9e35-a1cc-4310-a90d-c38e4c541bf1", "node_type": "4", "metadata": {"start_time": 360.14, "end_time": 366.98, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "9e56d6360c1c491ff5ee831acee4d6ab419d4f12e92dde1e5a8a38548b7014d8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "What if you really do understand if you really deeply get something you've been through that process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 101, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d92e2f63-7bf1-4ed8-84eb-fd9dfb3aecca": {"__data__": {"id_": "d92e2f63-7bf1-4ed8-84eb-fd9dfb3aecca", "embedding": null, "metadata": {"start_time": 367.2, "end_time": 395.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d629396f-04c1-4e41-b1a9-f981897ef1f4", "node_type": "4", "metadata": {"start_time": 367.2, "end_time": 395.4, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "d9ef7973887aae12e98145641682049dac7f2f57b5a0ee11de225321784a57ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "So you've seen how difficult it is and it's almost always difficult. So you're not going to wave your hands around and obfuscate and say, when you see someone doing that and you see it at university, I saw it with people who taught me, you can tell when they don't really understand something because they fall back on jargon and so wave their hands around and go, no, no, no. And usually that's because they've not been through that process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 442, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4c320d59-f30f-463c-a80a-8a3bb292e45b": {"__data__": {"id_": "4c320d59-f30f-463c-a80a-8a3bb292e45b", "embedding": null, "metadata": {"start_time": 395.72, "end_time": 422.6, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "38b86218-6666-4ee8-a536-1aaf6edea449", "node_type": "4", "metadata": {"start_time": 395.72, "end_time": 422.6, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "68cc4e89408fa93ebb71e095c25de55184bcda47e3caff593b2c163ef1ab1382", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And they're just, they're not tricking you, you know, but it's probably that they're tricking themselves into thinking that they understand. Are you comfortable saying, I don't know? I don't know. Oh, this is the basis of science. It's fundamental. I mean, <PERSON>, we mentioned you know, it's a <PERSON><PERSON><PERSON> Nobel Prize winning physicist also works on a Manhattan Project actually, one of the greats, a great teacher as well. And he called science a satisfactory philosophy of ignorance.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 491, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6eef3099-8d6e-4c6c-a08b-269e043f62d9": {"__data__": {"id_": "6eef3099-8d6e-4c6c-a08b-269e043f62d9", "embedding": null, "metadata": {"start_time": 423.04, "end_time": 442.26, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f57d1161-1c8b-449c-ae99-e75172ac83cd", "node_type": "4", "metadata": {"start_time": 423.04, "end_time": 442.26, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "a1670d40697779c690a428dcaeed00b9f799049a0fdc5b32057f32c8dbf94942", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And it's a really deep point actually because even that all knowledge starts from you, one, the individual, accepting that they don't know. You start with, I don't know how that works. I don't know why the sky is blue. I don't know why the leaves are green. I don't know why the universe is the way that it is.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 310, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6cab307a-7cd3-407d-ad3c-ea107bcffff6": {"__data__": {"id_": "6cab307a-7cd3-407d-ad3c-ea107bcffff6", "embedding": null, "metadata": {"start_time": 442.64, "end_time": 471.1, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "da0e8d8f-a4eb-45aa-a8ed-4fc00a6a56d8", "node_type": "4", "metadata": {"start_time": 442.64, "end_time": 471.1, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "652e504e2c876308e23c9a83b16dd54f0623c755426c532117866aceba216047", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And that's the you have to start from that point and then you build you try to build a reliable picture. It's a model of the world in your mind. But being a scientist, of course, it's about doing research. And that means that you're going to the edge of knowledge always and being extremely comfortable standing on the edge of the known, the dividing line between the known and the unknown and trying to find out a bit more. So you have to be delighted to not know.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 465, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3f52b115-84fb-4049-a71d-62d010b3bcd9": {"__data__": {"id_": "3f52b115-84fb-4049-a71d-62d010b3bcd9", "embedding": null, "metadata": {"start_time": 471.32, "end_time": 497.38, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9e404a5f-49e5-49e2-a63b-4c88f79acc40", "node_type": "4", "metadata": {"start_time": 471.32, "end_time": 497.38, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "24e380aab4603c61a6258a7ed40f0d2c7646acb35ab6b2721a07e76ba20643e6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "That's an excited about it to go back to what you said, passionate about not knowing in order to make any progress. And so I think that's a skill. It's about it's about jettisoning any fear of the unknown. Isn't it? And I think a lot of times we get into a lot of arguments as a society about things that are pretty unknowable, well unknowable at the moment. We don't know. So even basic things like did the universe have a beginning?", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 434, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "833cd018-0f09-420f-9dc1-a03bf6d24a2e": {"__data__": {"id_": "833cd018-0f09-420f-9dc1-a03bf6d24a2e", "embedding": null, "metadata": {"start_time": 497.84, "end_time": 515.22, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1d205454-5011-4033-aeea-52c2cfc6ad25", "node_type": "4", "metadata": {"start_time": 497.84, "end_time": 515.22, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "6a617750885f1e8eb937b19d5c8283d8d62142dc28348d1e2b6c62162dc08336", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Right and we know that the universe is very hot and dense 13.8 billion years ago with me that's good we call that the big bang but whether that was a beginning in time whether the universe existed in some form before that what it means to talk about the beginning of time we don't even know what time is.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 304, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e2a01810-4742-4238-a8e3-b847b0de33b1": {"__data__": {"id_": "e2a01810-4742-4238-a8e3-b847b0de33b1", "embedding": null, "metadata": {"start_time": 515.44, "end_time": 537.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "dfcb403f-b5e4-4e78-a110-23f7c23674c3", "node_type": "4", "metadata": {"start_time": 515.44, "end_time": 537.56, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "1979adbd4a59205c4f0bc4bfafcc4021e5bd4cd0aff0a5ce84b6060b4114f51c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Right, we've got some sense by the way that it might be built of smaller things but we can leave that alone. But the thing is, it's key, isn't it? Because a lot of people talk with great confidence about, well, how I know how the universe began, I know why the universe began. The answer is, how can you... Nobody knows, we don't know yet.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 339, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2dd0f6ea-90e1-4375-a062-55c6b0483a93": {"__data__": {"id_": "2dd0f6ea-90e1-4375-a062-55c6b0483a93", "embedding": null, "metadata": {"start_time": 537.52, "end_time": 546.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9ea26817-f414-45dd-bc5e-ed6330341c66", "node_type": "4", "metadata": {"start_time": 537.52, "end_time": 546.44, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "aad00aff3dfb2c2d409affb12a1661374d02b345f7d17e771f15135123467536", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And that's interesting and exciting. And that's the key, that's the starting point for building a reliable... But we live in a society where we...", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 146, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5864f7d0-8eec-458b-8653-73345a870947": {"__data__": {"id_": "5864f7d0-8eec-458b-8653-73345a870947", "embedding": null, "metadata": {"start_time": 546.94, "end_time": 572.84, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ee561a68-171d-4db9-ba73-397cb3b67f1b", "node_type": "4", "metadata": {"start_time": 546.94, "end_time": 572.84, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "a45ac9b85e9a4f7af68cd2dcabaee2e16e7f1789b0cb4ac533e143b95bb65ffd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Pillary people for not knowing. We demote people for saying I don't understand. We criticise people for not having all the answers. We promote people who bluff and bluster and trick their way to the top. We can say apolitical, but yeah. Whether that's political or even a business. The sort of people that are leading businesses are just those that are brave enough to say, I know all the answers, even if they don't.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 417, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "66594042-e133-4306-934e-4abe812eaeae": {"__data__": {"id_": "66594042-e133-4306-934e-4abe812eaeae", "embedding": null, "metadata": {"start_time": 573.06, "end_time": 601.88, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b4c888a3-5542-41b4-abbe-36138ca53be5", "node_type": "4", "metadata": {"start_time": 573.06, "end_time": 601.88, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "743c8a2087861b224fe1dbaac243f06e615b07b4ada2db0a9c0d9318f59c42c1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "We're all making it up. And I think it's really powerful if we, if you can sit here and say, I don't know, then that's great for everyone. Yeah. And if you think about it, so we do accept in certain professions that it's good to be, to know what you're doing. Like for example, flying a plane. Yeah. Being an airline captain. Doing an operation. The surgeon, the designer of a nuclear reactor, you know, there are things where knowledge and experience are valued.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 463, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "08cd5e5d-d0d0-423d-804a-5595f53746f0": {"__data__": {"id_": "08cd5e5d-d0d0-423d-804a-5595f53746f0", "embedding": null, "metadata": {"start_time": 602.12, "end_time": 629.9, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2467e199-f6b1-4c06-bdf4-7fec0e28779d", "node_type": "4", "metadata": {"start_time": 602.12, "end_time": 629.9, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "fe85c20ac2b6e5c66ac057ca7917a1689c3ed18b4b35c1d9dce0ee35a73f1931", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "But you're right. I suppose it's if you look at how our knowledge, and I used the term before, reliable knowledge, how that knowledge was acquired, it was acquired by people starting from the basis they didn't understand. And then the moment, this is critical in science, the moment that some new data appears, some new evidence, some new observation,", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 351, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "295311eb-8646-426b-ad28-140d090fc86a": {"__data__": {"id_": "295311eb-8646-426b-ad28-140d090fc86a", "embedding": null, "metadata": {"start_time": 630.56, "end_time": 655.04, "file_path": "transcript.json", "video_id": "temp_brian"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "39fe741f-1a41-4544-9113-7b4542313e1a", "node_type": "4", "metadata": {"start_time": 630.56, "end_time": 655.04, "file_path": "transcript.json", "video_id": "temp_brian"}, "hash": "cce921104d576a51d854a3b564c50da0177265aefc513629894c46b95ed777b8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "comes in that conflicts with your picture of the world then you start to be interested you start to be excited you go well if i'm wrong with my picture. Then i'm delighted because i've learned something i can rule that picture out and then i can go on to a new picture so you have to be delighted when you're shown to be wrong because you've learned something.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 360, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9afa3243-4b4e-4531-bf88-f2632287aa22": {"__data__": {"id_": "9afa3243-4b4e-4531-bf88-f2632287aa22", "embedding": null, "metadata": {"start_time": 0.54, "end_time": 26.38, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b3cbe17c-d42e-42ac-93ed-1b5607e2d5ef", "node_type": "4", "metadata": {"start_time": 0.54, "end_time": 26.38, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "87effb138f9d67a259c2db0884da9475f34a5492852ab18a62b6c32bd9f37edb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Hello everyone, myself <PERSON><PERSON>, main my friend <PERSON><PERSON> the way he is going to explain about computer absolute photography. Computer absolute photography or also known as CL is a volumetric 3D printing company developed by the researchers at UC Berkeley. It's not like general 3D printing techniques as general techniques is layer by layer printing whereas in CL we use light projections to create and 3D objects on the resume.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 426, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7a92c0a3-696d-4bc0-b09b-b6d290097af2": {"__data__": {"id_": "7a92c0a3-696d-4bc0-b09b-b6d290097af2", "embedding": null, "metadata": {"start_time": 27.64, "end_time": 50.6, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7e533cfa-8325-4892-9031-fc67f332e9bd", "node_type": "4", "metadata": {"start_time": 27.64, "end_time": 50.6, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "c74133722c6a5fd1cab6f47ff5d6221f923b8155b55ab5e0364c0cb993c8cf0a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "It takes inspiration from CT standing or CT imaging. But, in CT standing we try to reproduce 2D X-ray images from a 3D object. In CAL we use multiple 2D images of an object at different angles and try to create a 3D projection of 3D object from it. It uses the four principles of tomography and polypulmonaryization together.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 325, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2a1bb8b3-895a-4c83-a653-ddc4cb1f27a4": {"__data__": {"id_": "2a1bb8b3-895a-4c83-a653-ddc4cb1f27a4", "embedding": null, "metadata": {"start_time": 51.92, "end_time": 73.9, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0ef5a9ec-83c6-4e95-addd-26a3b58e1935", "node_type": "4", "metadata": {"start_time": 51.92, "end_time": 73.9, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "a946a57433da6e8d265d7c5b52df7de843933d5919d08e3a420a8a927a4f7768", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And he's a step by step procedure followed during the CAL for printing. He'll go through one by one. Then comes to step one, it's called 3D model design. We started with an CAD model of the object. Model is this list slides into 2D projections from multiple angles. Algorithms like a random transaction, transform are used to generate the slices.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 346, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "35d911c0-d00c-404f-abee-09dc1120f616": {"__data__": {"id_": "35d911c0-d00c-404f-abee-09dc1120f616", "embedding": null, "metadata": {"start_time": 74.14, "end_time": 100.16, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "99760400-57bd-4e01-b5ce-e44264ff8ab2", "node_type": "4", "metadata": {"start_time": 74.14, "end_time": 100.16, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "7e408cafaa4a47c07c4449f0d250624b50178affa0a31611e267930cdb10f1df", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And this slice is not typical layer used in VM or SLA. The light projection patterns used in CAL determine how the object will be built inside the resin volume. Step 2 is also called as a light pattern calculation. We create a sequence of 2D light images based on model slices. Each image corresponds to specific rotation angle. Light intensity is computed to ensure proper relative exposure.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 392, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8d6707d4-83ae-4676-9440-7c7d8d0bdb60": {"__data__": {"id_": "8d6707d4-83ae-4676-9440-7c7d8d0bdb60", "embedding": null, "metadata": {"start_time": 100.88, "end_time": 127.54, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6cc45592-fb25-42a3-b94b-ca5a547b0ecd", "node_type": "4", "metadata": {"start_time": 100.88, "end_time": 127.54, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "b6c9bbf6a5e6143962bd6e93181db80562e0cae959f702003f402c79ffece025", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "These patterns are designed so that when projected and combined during rotation, the resin resets enough cumulative light only in the desired regions. Light intensity at each angle is carefully controlled to prevent overexposure on the cutting. Step 3 is called as resin preparation. You fill and transfer in cylindrical control with photopolymerization resin. The resin should cure only about a specific light threshold.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 421, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "aeaf360b-cc08-4e9a-a331-98e379b0b666": {"__data__": {"id_": "aeaf360b-cc08-4e9a-a331-98e379b0b666", "embedding": null, "metadata": {"start_time": 127.76, "end_time": 154.22, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b65ac3c3-dd43-49e7-b562-2cc9f35869d9", "node_type": "4", "metadata": {"start_time": 127.76, "end_time": 154.22, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "de8fcc85631d28541f0323c10749302a3c79add35165417fc29bf39d360787dc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "mount container, you know, you put the mount container on a rotating platform. The key requirements for fail resins for our solute photopolymer is reasonable, oxygen inhibited, low light scattering and low viscosity and comes to step four. We feed the DLQ projector with the frames of images and the DLQ projector projects the 2D images while the resin and the", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 360, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4a2d0a6a-8a60-45ca-8533-4c796493cc78": {"__data__": {"id_": "4a2d0a6a-8a60-45ca-8533-4c796493cc78", "embedding": null, "metadata": {"start_time": 154.48, "end_time": 175.86, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e8718ede-0ca4-430f-a642-71818a7e12a0", "node_type": "4", "metadata": {"start_time": 154.48, "end_time": 175.86, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "487a6ae9b242c853b765a006d798a20b37b426c3f8288fbe97832e8b43ca32e3", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "container, it's rotated. Each image is particular specific angle synchronization rotation and projection ensure a preserved exposure. The step is crucial. It allows lies to build up at the right places across the full volume of the region and from here my friend <PERSON><PERSON> will be taking over. Thank you.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 301, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}