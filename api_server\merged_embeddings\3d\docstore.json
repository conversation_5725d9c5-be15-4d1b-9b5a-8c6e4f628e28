{"docstore/ref_doc_info": {"b3cbe17c-d42e-42ac-93ed-1b5607e2d5ef": {"node_ids": ["1be9915e-5633-4243-a37c-411808f5cffd"], "metadata": {"start_time": 0.54, "end_time": 26.38, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "7e533cfa-8325-4892-9031-fc67f332e9bd": {"node_ids": ["5f904578-f629-4673-85f1-393f34b5c027"], "metadata": {"start_time": 27.64, "end_time": 50.6, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "0ef5a9ec-83c6-4e95-addd-26a3b58e1935": {"node_ids": ["f7efbd1a-01f3-442d-a083-8a077ca48eca"], "metadata": {"start_time": 51.92, "end_time": 73.9, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "99760400-57bd-4e01-b5ce-e44264ff8ab2": {"node_ids": ["059fc9f9-5818-4417-a898-c5a153751d1d"], "metadata": {"start_time": 74.14, "end_time": 100.16, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "6cc45592-fb25-42a3-b94b-ca5a547b0ecd": {"node_ids": ["2b41a43a-6d7d-402d-bac0-2e12b9a630cf"], "metadata": {"start_time": 100.88, "end_time": 127.54, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "b65ac3c3-dd43-49e7-b562-2cc9f35869d9": {"node_ids": ["9a66433b-5b70-4b73-bf52-342f58f09bf2"], "metadata": {"start_time": 127.76, "end_time": 154.22, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}, "e8718ede-0ca4-430f-a642-71818a7e12a0": {"node_ids": ["5890d926-d383-4a8e-a7a1-18ce9bb9f419"], "metadata": {"start_time": 154.48, "end_time": 175.86, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}}}, "docstore/data": {"1be9915e-5633-4243-a37c-411808f5cffd": {"__data__": {"id_": "1be9915e-5633-4243-a37c-411808f5cffd", "embedding": null, "metadata": {"start_time": 0.54, "end_time": 26.38, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b3cbe17c-d42e-42ac-93ed-1b5607e2d5ef", "node_type": "4", "metadata": {"start_time": 0.54, "end_time": 26.38, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "87effb138f9d67a259c2db0884da9475f34a5492852ab18a62b6c32bd9f37edb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Hello everyone, myself <PERSON><PERSON>, main my friend <PERSON><PERSON> the way he is going to explain about computer absolute photography. Computer absolute photography or also known as CL is a volumetric 3D printing company developed by the researchers at UC Berkeley. It's not like general 3D printing techniques as general techniques is layer by layer printing whereas in CL we use light projections to create and 3D objects on the resume.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 426, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5f904578-f629-4673-85f1-393f34b5c027": {"__data__": {"id_": "5f904578-f629-4673-85f1-393f34b5c027", "embedding": null, "metadata": {"start_time": 27.64, "end_time": 50.6, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7e533cfa-8325-4892-9031-fc67f332e9bd", "node_type": "4", "metadata": {"start_time": 27.64, "end_time": 50.6, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "c74133722c6a5fd1cab6f47ff5d6221f923b8155b55ab5e0364c0cb993c8cf0a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "It takes inspiration from CT standing or CT imaging. But, in CT standing we try to reproduce 2D X-ray images from a 3D object. In CAL we use multiple 2D images of an object at different angles and try to create a 3D projection of 3D object from it. It uses the four principles of tomography and polypulmonaryization together.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 325, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f7efbd1a-01f3-442d-a083-8a077ca48eca": {"__data__": {"id_": "f7efbd1a-01f3-442d-a083-8a077ca48eca", "embedding": null, "metadata": {"start_time": 51.92, "end_time": 73.9, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0ef5a9ec-83c6-4e95-addd-26a3b58e1935", "node_type": "4", "metadata": {"start_time": 51.92, "end_time": 73.9, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "a946a57433da6e8d265d7c5b52df7de843933d5919d08e3a420a8a927a4f7768", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And he's a step by step procedure followed during the CAL for printing. He'll go through one by one. Then comes to step one, it's called 3D model design. We started with an CAD model of the object. Model is this list slides into 2D projections from multiple angles. Algorithms like a random transaction, transform are used to generate the slices.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 346, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "059fc9f9-5818-4417-a898-c5a153751d1d": {"__data__": {"id_": "059fc9f9-5818-4417-a898-c5a153751d1d", "embedding": null, "metadata": {"start_time": 74.14, "end_time": 100.16, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "99760400-57bd-4e01-b5ce-e44264ff8ab2", "node_type": "4", "metadata": {"start_time": 74.14, "end_time": 100.16, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "7e408cafaa4a47c07c4449f0d250624b50178affa0a31611e267930cdb10f1df", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "And this slice is not typical layer used in VM or SLA. The light projection patterns used in CAL determine how the object will be built inside the resin volume. Step 2 is also called as a light pattern calculation. We create a sequence of 2D light images based on model slices. Each image corresponds to specific rotation angle. Light intensity is computed to ensure proper relative exposure.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 392, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2b41a43a-6d7d-402d-bac0-2e12b9a630cf": {"__data__": {"id_": "2b41a43a-6d7d-402d-bac0-2e12b9a630cf", "embedding": null, "metadata": {"start_time": 100.88, "end_time": 127.54, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6cc45592-fb25-42a3-b94b-ca5a547b0ecd", "node_type": "4", "metadata": {"start_time": 100.88, "end_time": 127.54, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "b6c9bbf6a5e6143962bd6e93181db80562e0cae959f702003f402c79ffece025", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "These patterns are designed so that when projected and combined during rotation, the resin resets enough cumulative light only in the desired regions. Light intensity at each angle is carefully controlled to prevent overexposure on the cutting. Step 3 is called as resin preparation. You fill and transfer in cylindrical control with photopolymerization resin. The resin should cure only about a specific light threshold.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 421, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9a66433b-5b70-4b73-bf52-342f58f09bf2": {"__data__": {"id_": "9a66433b-5b70-4b73-bf52-342f58f09bf2", "embedding": null, "metadata": {"start_time": 127.76, "end_time": 154.22, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b65ac3c3-dd43-49e7-b562-2cc9f35869d9", "node_type": "4", "metadata": {"start_time": 127.76, "end_time": 154.22, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "de8fcc85631d28541f0323c10749302a3c79add35165417fc29bf39d360787dc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "mount container, you know, you put the mount container on a rotating platform. The key requirements for fail resins for our solute photopolymer is reasonable, oxygen inhibited, low light scattering and low viscosity and comes to step four. We feed the DLQ projector with the frames of images and the DLQ projector projects the 2D images while the resin and the", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 360, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5890d926-d383-4a8e-a7a1-18ce9bb9f419": {"__data__": {"id_": "5890d926-d383-4a8e-a7a1-18ce9bb9f419", "embedding": null, "metadata": {"start_time": 154.48, "end_time": 175.86, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e8718ede-0ca4-430f-a642-71818a7e12a0", "node_type": "4", "metadata": {"start_time": 154.48, "end_time": 175.86, "file_path": "transcript.json", "video_id": "Screen_Recording_2025-05-19_235025"}, "hash": "487a6ae9b242c853b765a006d798a20b37b426c3f8288fbe97832e8b43ca32e3", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "container, it's rotated. Each image is particular specific angle synchronization rotation and projection ensure a preserved exposure. The step is crucial. It allows lies to build up at the right places across the full volume of the region and from here my friend <PERSON><PERSON> will be taking over. Thank you.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 301, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}, "docstore/metadata": {"1be9915e-5633-4243-a37c-411808f5cffd": {"doc_hash": "6a34b16e9dbbfb04754985485187d1cce16ccd1d60c646f70c399c549a5d4bda", "ref_doc_id": "b3cbe17c-d42e-42ac-93ed-1b5607e2d5ef"}, "5f904578-f629-4673-85f1-393f34b5c027": {"doc_hash": "9f2a75b4c522d5e292bf6b952b0b96109b6fa415e305d54184226e747b3d912b", "ref_doc_id": "7e533cfa-8325-4892-9031-fc67f332e9bd"}, "f7efbd1a-01f3-442d-a083-8a077ca48eca": {"doc_hash": "d79b21e6e91a8efbc18e8ecfea4b3a0d953e43b060961dfdcdb3702f0e31a9d2", "ref_doc_id": "0ef5a9ec-83c6-4e95-addd-26a3b58e1935"}, "059fc9f9-5818-4417-a898-c5a153751d1d": {"doc_hash": "ebe625f88483692400f084c1346e8aeef53d674c12dfc9d06f83e4f3bd1d0ff3", "ref_doc_id": "99760400-57bd-4e01-b5ce-e44264ff8ab2"}, "2b41a43a-6d7d-402d-bac0-2e12b9a630cf": {"doc_hash": "35cd874a647137e944be555018ad562ef88f94e2ae1375f945fc95b82b3383c3", "ref_doc_id": "6cc45592-fb25-42a3-b94b-ca5a547b0ecd"}, "9a66433b-5b70-4b73-bf52-342f58f09bf2": {"doc_hash": "21fd7b05d70d4555bd6864528d99b38bef9a1f9257b18e237ef07507e65c6701", "ref_doc_id": "b65ac3c3-dd43-49e7-b562-2cc9f35869d9"}, "5890d926-d383-4a8e-a7a1-18ce9bb9f419": {"doc_hash": "4707393ed7cb7862a92c50fb762b17ff7c84c3499988a7bab8fbbec2706ea436", "ref_doc_id": "e8718ede-0ca4-430f-a642-71818a7e12a0"}}}