services:
  # Main API Server (Transcription, TTS, Video Processing) - CPU Only
  server:
    image: ${DOCKER_USERNAME:-your-dockerhub-username}/hpe-server:latest
    container_name: hpe-server-cpu
    ports:
      - "8000:8000"
    volumes:
      - ./api_server/transcripts:/app/transcripts
      - ./api_server/videos:/app/videos
      - ./api_server/audio_output:/app/audio_output
      - ./api_server/audio_only:/app/audio_only
    environment:
      - PYTHONUNBUFFERED=1
      - BACKEND_EMBED_URL=http://faiss_backend:8001/embed
      - GROQ_API_KEY=${GROQ_API_KEY}
    depends_on:
      - faiss_backend
    networks:
      - hpe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 1d
      timeout: 10s
      retries: 1
      start_period: 30s

  # FAISS Backend (Embeddings and Chat)
  faiss_backend:
    image: ${DOCKER_USERNAME:-your-dockerhub-username}/hpe-faiss-backend:latest
    container_name: hpe-faiss-backend
    ports:
      - "8001:8001"
    volumes:
      - ./api_server/transcripts:/app/transcripts
      - ./api_server/embeddings:/app/embeddings
      - ./api_server/merged_embeddings:/app/merged_embeddings
      - ./api_server/chat_mappings:/app/chat_mappings
    environment:
      - PYTHONUNBUFFERED=1
      - GROQ_API_KEY=${GROQ_API_KEY}
    networks:
      - hpe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 1d
      timeout: 10s
      retries: 1
      start_period: 30s

  # Frontend (React/Vite Application)
  frontend:
    image: ${DOCKER_USERNAME:-your-dockerhub-username}/hpe-frontend:latest
    container_name: hpe-frontend
    ports:
      - "80:80"
    depends_on:
      - server
      - faiss_backend
    networks:
      - hpe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 1d
      timeout: 10s
      retries: 1
      start_period: 30s

networks:
  hpe-network:
    driver: bridge

volumes:
  transcripts:
  videos:
  audio_output:
  embeddings:
  merged_embeddings:
  chat_mappings:
