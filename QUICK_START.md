# 🚀 Quick Start Guide - HPE Video Intelligence Platform

## For Users Who Just Want to Run the App

### What You Need
1. **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop/)
2. **GROQ API Key** - [Get free key here](https://console.groq.com/keys)

### Windows Users (3 Simple Steps)

#### Step 1: Download Files
Download these 3 files to a folder:
- `docker-compose.prod.yml`
- `.env.example` 
- `deploy.bat`

#### Step 2: Configure Your API Key
1. Copy `.env.example` to `.env`
2. Open `.env` in Notepad
3. Replace `your_groq_api_key_here` with your actual GROQ API key
4. Save the file

#### Step 3: Run the App
Double-click `deploy.bat` and wait for it to finish.

### Linux/Mac Users (3 Simple Steps)

#### Step 1: Download Files
Download these 3 files to a folder:
- `docker-compose.prod.yml`
- `.env.example`
- `deploy.sh`

#### Step 2: Configure Your API Key
```bash
cp .env.example .env
# Edit .env file and add your GROQ API key
```

#### Step 3: Run the App
```bash
chmod +x deploy.sh
./deploy.sh
```

## 🎉 That's It!

After deployment completes, open your browser and go to:
**http://localhost**

## 🎯 How to Use the App

### Upload a Video
1. Click "Upload Video" or paste a YouTube URL
2. Wait for transcription to complete
3. Start chatting about the video content

### Split-Screen Interface
- **Chat Panel**: Ask questions about your videos
- **Video Panel**: Click any timestamp in the chat to open the video player
- **Resize**: Drag the border between panels to adjust sizes
- **Close Video**: Click the X to return to full-width chat

### Features
- ✅ Upload MP4 files or YouTube URLs
- ✅ Automatic speech-to-text transcription
- ✅ AI-powered chat about video content
- ✅ Timestamp-based video navigation
- ✅ Semantic search through video content

## 🛠️ Troubleshooting

### App Won't Start
1. Make sure Docker Desktop is running
2. Check that ports 80, 8000, and 8001 are not in use
3. Verify your GROQ API key is correct in the `.env` file

### Can't Access the App
- Try: http://localhost
- If that doesn't work, try: http://localhost:80

### Video Won't Play
- Make sure your video file is in MP4 format
- For YouTube videos, ensure the URL is valid and accessible

## 📞 Need Help?

If you encounter issues:
1. Check the console output for error messages
2. Verify all environment variables are set correctly
3. Ensure Docker Desktop is running and has sufficient resources (8GB RAM recommended)

## 🔄 Updates

To get the latest version:
1. Stop the current deployment: `docker-compose -f docker-compose.prod.yml down`
2. Run the deploy script again: `deploy.bat` (Windows) or `./deploy.sh` (Linux/Mac)

The deployment script automatically pulls the latest images from Docker Hub.
