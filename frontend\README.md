# Frontend Documentation

This is the React frontend application for the voice-activated RAG application. It provides a modern, responsive user interface for interacting with the backend API server.

## Project Structure

```
frontend/
├── public/                # Static assets and index.html
├── src/
│   ├── App.jsx          # Main application component
│   ├── App.css          # App-specific styles
│   ├── main.jsx         # Entry point
│   ├── index.css        # Global styles
│   ├── assets/          # Application assets
│   └── components/      # React components
├── package.json        # Project dependencies
├── vite.config.js     # Vite configuration
├── tailwind.config.js # Tailwind CSS configuration
└── index.html         # Entry point HTML
```

### Core Components
- `App.jsx`: Main application component
- `main.jsx`: Application entry point
- `components/`: React components directory containing:
  - `ChatInterface.jsx`: UI for chat interactions with the RAG system
  - `FileUpload.jsx`: Component for uploading audio/video files
  - `TranscriptDetail.jsx`: Component for displaying detailed transcript information
  - `TranscriptList.jsx`: Component for listing available transcripts
- `assets/`: Directory for application assets

### Key Features
- Voice-activated interface
- File upload handling
- Transcript management
- Chat interface with audio output
- Real-time transcription display
- Video processing UI

### Configuration Files
- `vite.config.js`: Vite build configuration
- `tailwind.config.js`: Tailwind CSS configuration
- `package.json`: Project dependencies and scripts
- `eslint.config.js`: ESLint configuration
- `postcss.config.js`: PostCSS configuration

### Public Directory
- Contains static assets and index.html
- Used for deployment and static file serving

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript and enabling type-aware lint rules. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project.

---

## 🚀 Running with Docker

### **Prerequisites**
- Install Docker: [Docker Installation Guide](https://docs.docker.com/get-docker/)

### **Building and Running the Docker Container**

#### **Build the Docker Image**
```sh
docker build -t my-frontend .
```

### **Run the Container**
```sh
docker run -p 80:80 your-image-name
```

### **Stop and Remove the Container**
```sh
# Stop the Container
docker stop <container_id>

# Remove the Container
docker rm <container_id>
```

The frontend is served using Nginx on port 80. After building and running the container, you can access the application at `http://localhost`.
